# 美诗词网站

美诗词网站是一个展示中国古典诗词的平台，让用户领略中国古代诗词之美。

## 部署指南

### 本地开发环境

1. 安装依赖：
```bash
yarn install
```

2. 启动开发服务器：
```bash
yarn dev
```

### 生产环境部署

#### 构建项目

```bash
yarn build
```

构建完成后，将在 `dist` 目录下生成静态文件和服务器文件。

#### 启动服务

```bash
yarn start
```

或者使用 node 直接启动：

```bash
node dist/server/entry.mjs
```

#### 配置环境变量

可以通过设置以下环境变量来配置应用：

- `PORT`: 服务器监听端口 (默认: 4321)
- `HOST`: 服务器监听地址 (默认: 0.0.0.0)
- `NODE_ENV`: 运行环境 (production 或 development)

示例：

```bash
PORT=8080 HOST=localhost node dist/server/entry.mjs
```

### 使用 PM2 管理服务

可以使用 PM2 来管理Node.js应用进程：

1. 安装 PM2：
```bash
npm install -g pm2
```

2. 创建 `ecosystem.config.js` 文件：
```javascript
module.exports = {
  apps: [{
    name: "meishici",
    script: "./dist/server/entry.mjs",
    instances: "max",
    exec_mode: "cluster",
    env: {
      NODE_ENV: "production",
      PORT: 8080
    }
  }]
};
```

3. 使用 PM2 启动应用：
```bash
pm2 start ecosystem.config.js
```

4. 查看日志：
```bash
pm2 logs meishici
```

5. 监控应用：
```bash
pm2 monit
```

## 项目技术栈

- Astro.js
- React
- SolidJS
- Tailwind CSS
- SQLite 数据库

## 开源许可

本项目使用 MIT 许可证。

<h1 align=center>美诗词</h1>

<p align=center>https://meishici.com</p>

<p align=center>美诗词，领略中国古代诗词之美</p>

<p align=center>感谢您的支持与关注</p>

![image](public/images/screenshot.png)

## 📌 主要功能

- 🎯 美诗词按诗集、朝代、诗人、诗词等方式检索，内容丰富，信息齐全
- 📝 美诗词按选集、主题、节日、节气、词牌、时令、地理等方式精选分类
- 🔍 美诗词全站响应式布局，兼容移动端，支持暗黑模式，响应速度快


## 📱 古诗词数据来源

感谢各古诗词数据库提供的丰富资源


## 📝 版权信息

版权所有 © 2025 美诗词

**代码许可:** 基于 [MIT](LICENSE) 许可证发布。
