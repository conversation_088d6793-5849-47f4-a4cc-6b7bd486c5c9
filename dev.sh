#!/bin/bash

# 美诗词网站本地开发环境启动脚本

echo "=== 美诗词网站本地开发环境 ==="

# 检查yarn是否安装
if ! command -v yarn &> /dev/null; then
  echo "yarn未安装，正在安装..."
  npm install -g yarn
fi

# 安装依赖（如果node_modules不存在）
if [ ! -d "node_modules" ]; then
  echo "=== 安装依赖 ==="
  yarn install
fi

# 复制开发环境配置
if [ -f ".env.development" ]; then
  echo "=== 应用开发环境配置 ==="
  cp .env.development .env
fi

# 设置环境变量
export NODE_ENV=development
export PORT=4321
export HOST=localhost

# 启动开发服务器
echo "=== 启动开发服务器 ==="
echo "网站将在 http://localhost:4321 启动"
yarn dev 