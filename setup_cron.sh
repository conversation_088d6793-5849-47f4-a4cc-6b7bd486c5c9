#!/bin/bash

# 设置每日更新的定时任务，每天凌晨3点更新网站内容

SITE_PATH="/var/www/meishici"  # 网站路径，根据实际情况修改
LOG_PATH="$SITE_PATH/logs"
SCRIPT_PATH="$SITE_PATH/scripts/update_daily.js"

# 创建日志目录
mkdir -p $LOG_PATH

# 创建定时任务执行脚本
cat > $SITE_PATH/run_daily_update.sh << EOF
#!/bin/bash
cd $SITE_PATH
node $SCRIPT_PATH >> $LOG_PATH/daily_update.log 2>&1
EOF

# 设置执行权限
chmod +x $SITE_PATH/run_daily_update.sh

# 检查当前用户的crontab中是否已存在相关任务
EXISTING_CRON=\$(crontab -l 2>/dev/null | grep -c "run_daily_update.sh")

if [ \$EXISTING_CRON -eq 0 ]; then
  # 添加新的定时任务：每天凌晨3点执行
  (crontab -l 2>/dev/null; echo "0 3 * * * $SITE_PATH/run_daily_update.sh") | crontab -
  echo "已添加每日更新定时任务，每天凌晨3点执行"
else
  echo "每日更新定时任务已存在，无需重复添加"
fi

# 显示当前的定时任务列表
echo "当前定时任务列表:"
crontab -l

echo "完成！每日更新任务已设置" 