yarn run v1.22.22
warning package.json: "dependencies" has dependency "prettier-plugin-tailwindcss" with range "^0.5.9" that collides with a dependency in "devDependencies" of the same name with version "^0.5.5"
$ yarn build_search && yarn build_date && yarn migrate_pageviews && yarn fix_long_slugs && astro build --verbose
warning package.json: "dependencies" has dependency "prettier-plugin-tailwindcss" with range "^0.5.9" that collides with a dependency in "devDependencies" of the same name with version "^0.5.5"
$ node scripts/build_search.js
search.json has been generated successfully.
warning package.json: "dependencies" has dependency "prettier-plugin-tailwindcss" with range "^0.5.9" that collides with a dependency in "devDependencies" of the same name with version "^0.5.5"
$ node scripts/build_date.js
date.json has been generated successfully.
warning package.json: "dependencies" has dependency "prettier-plugin-tailwindcss" with range "^0.5.9" that collides with a dependency in "devDependencies" of the same name with version "^0.5.5"
$ node scripts/migrate_pageviews.js
✓ 页面访问统计表已存在
✓ 数据库迁移完成
warning package.json: "dependencies" has dependency "prettier-plugin-tailwindcss" with range "^0.5.9" that collides with a dependency in "devDependencies" of the same name with version "^0.5.5"
$ node scripts/fix_long_slugs.js
开始修复过长的slug字段...
项目目录: /root/meisici/shici001
数据库路径: /root/meisici/shici001/src/database/poetry.db
发现0个过长的slug需要修复
没有发现过长的slug，无需修复
2025-04-11T04:01:58.345Z astro:cli --verbose flag enabled! Enabling: DEBUG="astro:*,vite:*"
2025-04-11T04:01:58.346Z astro:cli Tip: Set the DEBUG env variable directly for more control. Example: "DEBUG=astro:*,vite:* astro build".
2025-04-11T04:01:59.178Z astro:telemetry [notify] last notified on 1744115353817
2025-04-11T04:01:59.845Z astro:Failed to load config with Node TypeError [ERR_IMPORT_ASSERTION_TYPE_MISSING]: Module "file:///root/meisici/shici001/src/config/config.json" needs an import attribute of type "json"
    at new NodeError (node:internal/errors:405:5)
    at validateAttributes (node:internal/modules/esm/assert:89:15)
    at defaultLoad (node:internal/modules/esm/load:103:3)
    at ModuleLoader.load (node:internal/modules/esm/loader:417:13)
    at ModuleLoader.moduleProvider (node:internal/modules/esm/loader:288:22)
    at new ModuleJob (node:internal/modules/esm/module_job:63:26)
    at #createModuleJob (node:internal/modules/esm/loader:312:17)
    at ModuleLoader.getJobFromResolveResult (node:internal/modules/esm/loader:265:34)
    at ModuleLoader.getModuleJob (node:internal/modules/esm/loader:251:17)
    at async ModuleWrap.<anonymous> (node:internal/modules/esm/module_job:76:21)
2025-04-11T04:01:59.862Z vite:config using resolved config: {
  configFile: undefined,
  server: {
    preTransformRequests: true,
    middlewareMode: true,
    hmr: false,
    watch: null,
    ws: false,
    sourcemapIgnoreList: [Function: isInNodeModules$1],
    fs: {
      strict: true,
      allow: [Array],
      deny: [Array],
      cachedChecks: undefined
    }
  },
  optimizeDeps: {
    holdUntilCrawlEnd: true,
    noDiscovery: true,
    esbuildOptions: { preserveSymlinks: false }
  },
  clearScreen: false,
  appType: 'custom',
  ssr: {
    target: 'node',
    external: [
      '@astrojs/tailwind',
      '@astrojs/mdx',
      '@astrojs/react',
      '@astrojs/preact',
      '@astrojs/sitemap',
      '@astrojs/markdoc',
      '@astrojs/db'
    ],
    optimizeDeps: { noDiscovery: true, esbuildOptions: [Object] }
  },
  plugins: [
    'vite:watch-package-data',
    'vite:pre-alias',
    'alias',
    'vite:modulepreload-polyfill',
    'vite:resolve',
    'vite:html-inline-proxy',
    'vite:css',
    'vite:esbuild',
    'vite:json',
    'vite:wasm-helper',
    'vite:worker',
    'vite:asset',
    'vite:wasm-fallback',
    'vite:define',
    'vite:css-post',
    'vite:worker-import-meta-url',
    'vite:asset-import-meta-url',
    'vite:dynamic-import-vars',
    'vite:import-glob',
    'vite:client-inject',
    'vite:css-analysis',
    'vite:import-analysis'
  ],
  configFileDependencies: [],
  inlineConfig: {
    configFile: false,
    server: { middlewareMode: true, hmr: false, watch: null, ws: false },
    optimizeDeps: { noDiscovery: true },
    clearScreen: false,
    appType: 'custom',
    ssr: { external: [Array] },
    plugins: [ false ]
  },
  root: '/root/meisici/shici001',
  base: '/',
  decodedBase: '/',
  rawBase: '/',
  resolve: {
    mainFields: [ 'browser', 'module', 'jsnext:main', 'jsnext' ],
    conditions: [],
    extensions: [
      '.mjs',  '.js',
      '.mts',  '.ts',
      '.jsx',  '.tsx',
      '.json'
    ],
    dedupe: [],
    preserveSymlinks: false,
    alias: [ [Object], [Object] ]
  },
  publicDir: '/root/meisici/shici001/public',
  cacheDir: '/root/meisici/shici001/node_modules/.vite',
  command: 'serve',
  mode: 'development',
  isWorker: false,
  mainConfig: null,
  bundleChain: [],
  isProduction: true,
  css: { lightningcss: undefined },
  esbuild: { jsxDev: false },
  build: {
    target: [ 'es2020', 'edge88', 'firefox78', 'chrome87', 'safari14' ],
    cssTarget: [ 'es2020', 'edge88', 'firefox78', 'chrome87', 'safari14' ],
    outDir: 'dist',
    assetsDir: 'assets',
    assetsInlineLimit: 4096,
    cssCodeSplit: true,
    sourcemap: false,
    rollupOptions: {},
    minify: 'esbuild',
    terserOptions: {},
    write: true,
    emptyOutDir: null,
    copyPublicDir: true,
    manifest: false,
    lib: false,
    ssr: false,
    ssrManifest: false,
    ssrEmitAssets: false,
    reportCompressedSize: true,
    chunkSizeWarningLimit: 500,
    watch: null,
    commonjsOptions: { include: [Array], extensions: [Array] },
    dynamicImportVarsOptions: { warnOnError: true, exclude: [Array] },
    modulePreload: { polyfill: true },
    cssMinify: true
  },
  preview: {
    port: undefined,
    strictPort: undefined,
    host: undefined,
    allowedHosts: undefined,
    https: undefined,
    open: undefined,
    proxy: undefined,
    cors: undefined,
    headers: undefined
  },
  envDir: '/root/meisici/shici001',
  env: {
    VITE_USER_NODE_ENV: 'development',
    BASE_URL: '/',
    MODE: 'development',
    DEV: false,
    PROD: true
  },
  assetsInclude: [Function: assetsInclude],
  logger: {
    hasWarned: false,
    info: [Function: info],
    warn: [Function: warn],
    warnOnce: [Function: warnOnce],
    error: [Function: error],
    clearScreen: [Function: clearScreen],
    hasErrorLogged: [Function: hasErrorLogged]
  },
  packageCache: Map(1) {
    'fnpd_/root/meisici/shici001' => {
      dir: '/root/meisici/shici001',
      data: [Object],
      hasSideEffects: [Function: hasSideEffects],
      webResolvedImports: {},
      nodeResolvedImports: {},
      setResolvedCache: [Function: setResolvedCache],
      getResolvedCache: [Function: getResolvedCache]
    },
    set: [Function (anonymous)]
  },
  createResolver: [Function: createResolver],
  worker: { format: 'iife', plugins: '() => plugins', rollupOptions: {} },
  experimental: { importGlobRestoreExtension: false, hmrPartialAccept: false },
  webSocketToken: 'AruzZM_esFNq',
  additionalAllowedHosts: [],
  getSortedPlugins: [Function: getSortedPlugins],
  getSortedPluginHooks: [Function: getSortedPluginHooks]
}
2025-04-11T04:01:59.975Z vite:load 75.34ms [fs] astro.config.mjs
2025-04-11T04:01:59.988Z vite:resolve 1.62ms ./src/config/config.json -> /root/meisici/shici001/src/config/config.json
2025-04-11T04:01:59.990Z vite:resolve 0.56ms /src/config/config.json -> /root/meisici/shici001/src/config/config.json
2025-04-11T04:01:59.991Z vite:import-analysis 11.35ms [1 imports rewritten] astro.config.mjs
2025-04-11T04:01:59.991Z vite:transform 15.94ms astro.config.mjs
2025-04-11T04:02:00.057Z vite:load 65.20ms [fs] /src/config/config.json
2025-04-11T04:02:00.060Z vite:import-analysis [skipped] src/config/config.json
2025-04-11T04:02:00.060Z vite:transform 2.38ms /src/config/config.json
2025-04-11T04:02:01.435Z astro:telemetry {
  context: {
    isGit: false,
    anonymousProjectId: 'b8c87cb47d46158dffa5c284c73d0cc2953984ddd37ee60a6696ba49833dcd74',
    packageManager: 'yarn',
    packageManagerVersion: '1.22.22',
    anonymousId: '5a5115b0b49fc0a18f573c291601b62b8ac07ac2964680bbea53da6ac01b6994',
    anonymousSessionId: 'b171f32c861da7e77e2391d1f61f000b71f330046f4fb3e8a60dd431641eada1'
  },
  meta: {
    nodeVersion: '18.20.8',
    viteVersion: '5.4.18',
    astroVersion: '4.16.18',
    systemPlatform: 'linux',
    systemRelease: '6.1.0-28-amd64',
    systemArchitecture: 'x64',
    cpuCount: 2,
    cpuModel: 'Intel(R) Xeon(R) Platinum 8255C CPU @ 2.50GHz',
    cpuSpeed: 2499,
    memoryInMb: 1966,
    isDocker: false,
    isTTY: undefined,
    isWSL: false,
    isCI: false,
    ciName: null
  }
}
2025-04-11T04:02:01.435Z astro:telemetry [
  {
    "eventName": "ASTRO_CLI_SESSION_STARTED",
    "payload": {
      "cliCommand": "build",
      "config": {
        "site": true,
        "base": true,
        "trailingSlash": "never",
        "output": "server",
        "adapter": "@astrojs/node",
        "integrations": [
          "@astrojs/react",
          "@astrojs/sitemap",
          "@astrojs/tailwind",
          "auto-import",
          "@astrojs/mdx",
          "astro-icon",
          "@astrojs/partytown"
        ],
        "build": {},
        "server": true,
        "image": {
          "service": true
        },
        "markdown": {
          "shikiConfig": true,
          "remarkPlugins": true
        },
        "experimental": {},
        "legacy": {}
      }
    }
  }
]
2025-04-11T04:02:01.453Z astro:build Initial setup...
2025-04-11T04:02:01.582Z vite:config using resolved config: {
  configFile: undefined,
  cacheDir: '/root/meisici/shici001/node_modules/.vite',
  clearScreen: false,
  customLogger: {
    hasWarned: false,
    info: [Function: info],
    warn: [Function: warn],
    warnOnce: [Function: warnOnce],
    error: [Function: error],
    clearScreen: [Function: clearScreen],
    hasErrorLogged: [Function: hasErrorLogged]
  },
  appType: 'custom',
  optimizeDeps: {
    holdUntilCrawlEnd: true,
    entries: [
      '/root/meisici/shici001/src/**/*.{jsx,tsx,vue,svelte,html,astro}'
    ],
    exclude: [ 'astro', 'node-fetch', '@astrojs/react/server.js' ],
    include: [
      '@astrojs/react/client.js',
      'react',
      'react/jsx-runtime',
      'react/jsx-dev-runtime',
      'react-dom',
      'react',
      'react-dom',
      'react/jsx-dev-runtime',
      'react/jsx-runtime',
      'astro > cssesc',
      'astro > aria-query',
      'astro > axobject-query'
    ],
    noDiscovery: true,
    esbuildOptions: { preserveSymlinks: false, jsx: 'automatic' }
  },
  plugins: [
    'vite:optimized-deps',
    'vite:watch-package-data',
    'vite:pre-alias',
    'alias',
    'astro:build',
    'astro:markdown',
    '@mdx-js/rollup',
    'astro:jsx',
    'astro:head-metadata',
    'astro-content-virtual-mod-plugin',
    'astro:content-asset-propagation',
    'astro:assets:esm',
    'astro:vite-plugin-file-url',
    'astro:i18n',
    'astro:actions',
    'astro:container',
    'vite:react-babel',
    'vite:react-refresh',
    'vite:modulepreload-polyfill',
    'vite:resolve',
    'vite:html-inline-proxy',
    'vite:css',
    'vite:esbuild',
    'vite:json',
    'vite:wasm-helper',
    'vite:worker',
    'vite:asset',
    'astro:build:normal',
    'astro:scripts',
    'astro:html',
    'astro:postprocess',
    'astro:integration-container',
    'astro:content-imports',
    '@astro/plugin-middleware',
    'astro:assets',
    'astro:prefetch',
    'astro:transitions',
    'astro:dev-toolbar',
    '@astro/plugin-actions',
    '@astrojs/react:opts',
    '@astrojs/mdx-postprocess',
    'astro-icon',
    'vite:wasm-fallback',
    'astro:vite-plugin-env',
    'vite:define',
    'vite:css-post',
    'vite:worker-import-meta-url',
    'vite:asset-import-meta-url',
    'vite:dynamic-import-vars',
    'vite:import-glob',
    'astro:tsconfig-alias',
    'astro:scripts:page-ssr',
    'astro:scanner',
    '@astrojs/vite-plugin-astro-ssr-manifest',
    'vite:client-inject',
    'vite:css-analysis',
    'vite:import-analysis'
  ],
  publicDir: '/root/meisici/shici001/public',
  root: '/root/meisici/shici001',
  envPrefix: 'PUBLIC_',
  define: {
    'import.meta.env.SITE': '"https://mokk.cn"',
    'import.meta.env.BASE_URL': '"/"',
    'import.meta.env.ASSETS_PREFIX': 'undefined',
    __ASTRO_INTERNAL_I18N_CONFIG__: '{"base":"/","format":"directory","site":"https://mokk.cn","trailingSlash":"never","isBuild":false}'
  },
  server: {
    preTransformRequests: true,
    hmr: false,
    watch: { ignored: [Array] },
    middlewareMode: true,
    ws: false,
    sourcemapIgnoreList: [Function: isInNodeModules$1],
    fs: {
      strict: true,
      allow: [Array],
      deny: [Array],
      cachedChecks: undefined
    }
  },
  resolve: {
    mainFields: [ 'browser', 'module', 'jsnext:main', 'jsnext' ],
    conditions: [ 'astro' ],
    extensions: [
      '.mjs',  '.js',
      '.mts',  '.ts',
      '.jsx',  '.tsx',
      '.json'
    ],
    dedupe: [
      'astro',
      'react',
      'react-dom',
      'react-dom/server',
      'react',
      'react-dom'
    ],
    preserveSymlinks: false,
    alias: [
      [Object], [Object],
      [Object], [Object],
      [Object], [Object],
      [Object]
    ]
  },
  ssr: {
    target: 'node',
    noExternal: [
      'astro',
      'astro/components',
      '@nanostores/preact',
      '@fontsource/*',
      '@astrojs/mdx',
      '@astrojs/node',
      '@astrojs/partytown',
      '@astrojs/react',
      '@astrojs/sitemap',
      '@astrojs/solid-js',
      '@astrojs/tailwind',
      'astro-auto-import',
      'astro-font',
      'astro-icon',
      'astro-loading-indicator',
      '@mui/material',
      '@mui/base',
      '@babel/runtime',
      'use-immer',
      '@material-tailwind/react',
      '@astrojs/node'
    ],
    external: [ 'react-dom/server', 'react-dom/client' ],
    optimizeDeps: { noDiscovery: true, esbuildOptions: [Object] }
  },
  build: {
    target: [ 'es2020', 'edge88', 'firefox78', 'chrome87', 'safari14' ],
    cssTarget: [ 'es2020', 'edge88', 'firefox78', 'chrome87', 'safari14' ],
    outDir: 'dist',
    assetsDir: '_astro',
    assetsInlineLimit: 4096,
    cssCodeSplit: true,
    sourcemap: false,
    rollupOptions: { onwarn: [Function: onwarn] },
    minify: 'esbuild',
    terserOptions: {},
    write: true,
    emptyOutDir: null,
    copyPublicDir: true,
    manifest: false,
    lib: false,
    ssr: false,
    ssrManifest: false,
    ssrEmitAssets: false,
    reportCompressedSize: true,
    chunkSizeWarningLimit: 500,
    watch: null,
    commonjsOptions: { include: [Array], extensions: [Array] },
    dynamicImportVarsOptions: { warnOnError: true, exclude: [Array] },
    modulePreload: { polyfill: true },
    cssMinify: true
  },
  css: {
    postcss: {
      cwd: '/root/meisici/shici001',
      env: 'production',
      plugins: [Array]
    },
    lightningcss: undefined
  },
  logLevel: 'silent',
  esbuild: { jsxDev: false, jsx: 'automatic', jsxImportSource: undefined },
  configFileDependencies: [],
  inlineConfig: {
    configFile: false,
    cacheDir: '/root/meisici/shici001/node_modules/.vite/',
    clearScreen: false,
    customLogger: {
      hasWarned: false,
      info: [Function: info],
      warn: [Function: warn],
      warnOnce: [Function: warnOnce],
      error: [Function: error],
      clearScreen: [Function: clearScreen],
      hasErrorLogged: [Function: hasErrorLogged]
    },
    appType: 'custom',
    optimizeDeps: {
      entries: [Array],
      exclude: [Array],
      include: [Array],
      noDiscovery: true
    },
    plugins: [
      [Object], false,    [Array],   [Object],
      false,    [Object], undefined, [Object],
      [Object], [Object], [Object],  [Object],
      [Object], [Object], [Object],  [Object],
      [Array],  [Object], [Object],  [Object],
      [Array],  [Object], [Object],  [Object],
      [Object], [Object], [Object],  [Object],
      false,    [Object], [Object],  [Object],
      [Object], [Object], [Object],  [Object]
    ],
    publicDir: '/root/meisici/shici001/public/',
    root: '/root/meisici/shici001/',
    envPrefix: 'PUBLIC_',
    define: {
      'import.meta.env.SITE': '"https://mokk.cn"',
      'import.meta.env.BASE_URL': '"/"',
      'import.meta.env.ASSETS_PREFIX': 'undefined'
    },
    server: { hmr: false, watch: [Object], middlewareMode: true, ws: false },
    resolve: { alias: [Array], conditions: [Array], dedupe: [Array] },
    ssr: { noExternal: [Array], external: [Array] },
    build: { assetsDir: '_astro' },
    css: { postcss: [Object] },
    logLevel: 'silent'
  },
  base: '/',
  decodedBase: '/',
  rawBase: '/',
  command: 'serve',
  mode: 'development',
  isWorker: false,
  mainConfig: null,
  bundleChain: [],
  isProduction: true,
  preview: {
    port: undefined,
    strictPort: undefined,
    host: undefined,
    allowedHosts: undefined,
    https: undefined,
    open: undefined,
    proxy: undefined,
    cors: undefined,
    headers: undefined
  },
  envDir: '/root/meisici/shici001',
  env: {
    PUBLIC_BASE_URL: 'http://localhost:4321',
    BASE_URL: '/',
    MODE: 'development',
    DEV: false,
    PROD: true
  },
  assetsInclude: [Function: assetsInclude],
  logger: {
    hasWarned: false,
    info: [Function: info],
    warn: [Function: warn],
    warnOnce: [Function: warnOnce],
    error: [Function: error],
    clearScreen: [Function: clearScreen],
    hasErrorLogged: [Function: hasErrorLogged]
  },
  packageCache: Map(1) {
    'fnpd_/root/meisici/shici001' => {
      dir: '/root/meisici/shici001',
      data: [Object],
      hasSideEffects: [Function: hasSideEffects],
      webResolvedImports: {},
      nodeResolvedImports: {},
      setResolvedCache: [Function: setResolvedCache],
      getResolvedCache: [Function: getResolvedCache]
    },
    set: [Function (anonymous)]
  },
  createResolver: [Function (anonymous)],
  worker: { format: 'iife', plugins: '() => plugins', rollupOptions: {} },
  experimental: { importGlobRestoreExtension: false, hmrPartialAccept: false },
  webSocketToken: '9ezTsaC8sW5I',
  additionalAllowedHosts: [],
  getSortedPlugins: [Function: getSortedPlugins],
  getSortedPluginHooks: [Function: getSortedPluginHooks]
}
2025-04-11T04:02:01.602Z vite:resolve 1.91ms astro/assets/endpoint/node -> /root/meisici/shici001/node_modules/astro/dist/assets/endpoint/node.js
2025-04-11T04:02:01.603Z vite:deps removing old cache dir /root/meisici/shici001/node_modules/.vite/deps
2025-04-11T04:02:01.608Z vite:resolve 0.56ms @astrojs/react/client.js -> /root/meisici/shici001/node_modules/@astrojs/react/client.js
2025-04-11T04:02:01.609Z vite:resolve 0.46ms react -> /root/meisici/shici001/node_modules/react/index.js
2025-04-11T04:02:01.609Z vite:resolve 0.18ms react/jsx-runtime -> /root/meisici/shici001/node_modules/react/jsx-runtime.js
2025-04-11T04:02:01.609Z vite:resolve 0.17ms react/jsx-dev-runtime -> /root/meisici/shici001/node_modules/react/jsx-dev-runtime.js
2025-04-11T04:02:01.610Z vite:resolve 0.42ms react-dom -> /root/meisici/shici001/node_modules/react-dom/index.js
2025-04-11T04:02:01.611Z vite:resolve 0.84ms cssesc -> /root/meisici/shici001/node_modules/cssesc/cssesc.js
2025-04-11T04:02:01.612Z vite:resolve 0.37ms aria-query -> /root/meisici/shici001/node_modules/aria-query/lib/index.js
2025-04-11T04:02:01.612Z vite:resolve 0.38ms axobject-query -> /root/meisici/shici001/node_modules/axobject-query/lib/index.js
2025-04-11T04:02:01.614Z vite:deps creating package.json in /root/meisici/shici001/node_modules/.vite/deps_temp_f7e63c22
2025-04-11T04:02:01.647Z vite:load 2.74ms [fs] src/content/config.ts
2025-04-11T04:02:01.660Z vite:resolve 0.74ms react -> /root/meisici/shici001/node_modules/react/index.js
2025-04-11T04:02:01.663Z vite:resolve 0.40ms react-dom/client -> /root/meisici/shici001/node_modules/react-dom/client.js
2025-04-11T04:02:01.700Z vite:resolve 0.04ms astro:content ->  astro:content
2025-04-11T04:02:01.702Z vite:resolve 1.02ms  astro:content -> null
2025-04-11T04:02:01.702Z vite:import-analysis 7.28ms [1 imports rewritten] src/content/config.ts
2025-04-11T04:02:01.702Z vite:transform 54.48ms src/content/config.ts
2025-04-11T04:02:01.721Z vite:resolve 1.27ms react-dom -> /root/meisici/shici001/node_modules/react-dom/index.js
2025-04-11T04:02:01.773Z vite:resolve 1.70ms react -> /root/meisici/shici001/node_modules/react/index.js
2025-04-11T04:02:01.791Z vite:resolve 0.47ms scheduler -> /root/meisici/shici001/node_modules/scheduler/index.js
2025-04-11T04:02:01.813Z vite:load 108.47ms [plugin]  astro:content
2025-04-11T04:02:01.862Z vite:resolve 2.24ms astro/content/runtime -> /root/meisici/shici001/node_modules/astro/dist/content/runtime.js
2025-04-11T04:02:01.863Z vite:resolve 2.86ms astro/zod -> /root/meisici/shici001/node_modules/astro/zod.mjs
2025-04-11T04:02:01.863Z vite:resolve 3.19ms /src/content/about/-index.md?astroContentCollectionEntry=true -> /root/meisici/shici001/src/content/about/-index.md?astroContentCollectionEntry=true
2025-04-11T04:02:01.863Z vite:resolve 3.33ms /src/content/pages/privacy-policy.md?astroContentCollectionEntry=true -> /root/meisici/shici001/src/content/pages/privacy-policy.md?astroContentCollectionEntry=true
2025-04-11T04:02:01.864Z vite:resolve 3.65ms /src/content/pages/terms-of-service.md?astroContentCollectionEntry=true -> /root/meisici/shici001/src/content/pages/terms-of-service.md?astroContentCollectionEntry=true
2025-04-11T04:02:01.864Z vite:resolve 3.80ms /src/content/sections/hai-tang-shi-she.md?astroContentCollectionEntry=true -> /root/meisici/shici001/src/content/sections/hai-tang-shi-she.md?astroContentCollectionEntry=true
2025-04-11T04:02:01.864Z vite:resolve 3.99ms /src/content/sections/testimonial.md?astroContentCollectionEntry=true -> /root/meisici/shici001/src/content/sections/testimonial.md?astroContentCollectionEntry=true
2025-04-11T04:02:01.872Z vite:resolve 11.23ms /src/content/about/-index.md?astroPropagatedAssets -> /root/meisici/shici001/src/content/about/-index.md?astroPropagatedAssets
2025-04-11T04:02:01.874Z vite:resolve 12.66ms /src/content/pages/privacy-policy.md?astroPropagatedAssets -> /root/meisici/shici001/src/content/pages/privacy-policy.md?astroPropagatedAssets
2025-04-11T04:02:01.874Z vite:resolve 12.80ms /src/content/pages/terms-of-service.md?astroPropagatedAssets -> /root/meisici/shici001/src/content/pages/terms-of-service.md?astroPropagatedAssets
2025-04-11T04:02:01.874Z vite:resolve 12.92ms /src/content/sections/hai-tang-shi-she.md?astroPropagatedAssets -> /root/meisici/shici001/src/content/sections/hai-tang-shi-she.md?astroPropagatedAssets
2025-04-11T04:02:01.874Z vite:resolve 13.19ms /src/content/sections/testimonial.md?astroPropagatedAssets -> /root/meisici/shici001/src/content/sections/testimonial.md?astroPropagatedAssets
2025-04-11T04:02:01.875Z vite:resolve 14.65ms /src/content/about/-index.md?astroRenderContent=true -> /root/meisici/shici001/src/content/about/-index.md?astroPropagatedAssets
2025-04-11T04:02:01.875Z vite:resolve 14.90ms /src/content/pages/privacy-policy.md?astroRenderContent=true -> /root/meisici/shici001/src/content/pages/privacy-policy.md?astroPropagatedAssets
2025-04-11T04:02:01.876Z vite:resolve 15.79ms /src/content/pages/terms-of-service.md?astroRenderContent=true -> /root/meisici/shici001/src/content/pages/terms-of-service.md?astroPropagatedAssets
2025-04-11T04:02:01.876Z vite:resolve 16.00ms /src/content/sections/hai-tang-shi-she.md?astroRenderContent=true -> /root/meisici/shici001/src/content/sections/hai-tang-shi-she.md?astroPropagatedAssets
2025-04-11T04:02:01.876Z vite:resolve 16.16ms /src/content/sections/testimonial.md?astroRenderContent=true -> /root/meisici/shici001/src/content/sections/testimonial.md?astroPropagatedAssets
2025-04-11T04:02:01.881Z vite:resolve 4.54ms /node_modules/astro/dist/content/runtime.js -> /root/meisici/shici001/node_modules/astro/dist/content/runtime.js
2025-04-11T04:02:01.881Z vite:resolve 4.90ms /node_modules/astro/zod.mjs -> /root/meisici/shici001/node_modules/astro/zod.mjs
2025-04-11T04:02:01.887Z vite:import-analysis 28.51ms [12 imports rewritten]  astro:content
2025-04-11T04:02:01.888Z vite:transform 74.15ms  astro:content
2025-04-11T04:02:01.933Z vite:load 43.15ms [fs] /node_modules/astro/dist/content/runtime.js
2025-04-11T04:02:02.007Z vite:resolve 0.21ms astro:asset-imports ->  astro:asset-imports
2025-04-11T04:02:02.007Z vite:resolve 0.52ms astro:content-module-imports ->  astro:content-module-imports
2025-04-11T04:02:02.008Z vite:resolve 1.58ms ../assets/utils/resolveImports.js -> /root/meisici/shici001/node_modules/astro/dist/assets/utils/resolveImports.js
2025-04-11T04:02:02.008Z vite:resolve 1.76ms ../core/errors/index.js -> /root/meisici/shici001/node_modules/astro/dist/core/errors/index.js
2025-04-11T04:02:02.008Z vite:resolve 1.86ms ../core/path.js -> /root/meisici/shici001/node_modules/astro/dist/core/path.js
2025-04-11T04:02:02.008Z vite:resolve 1.95ms ../runtime/server/index.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/index.js
2025-04-11T04:02:02.009Z vite:resolve 2.17ms ./consts.js -> /root/meisici/shici001/node_modules/astro/dist/content/consts.js
2025-04-11T04:02:02.009Z vite:resolve 2.29ms ./data-store.js -> /root/meisici/shici001/node_modules/astro/dist/content/data-store.js
2025-04-11T04:02:02.009Z vite:resolve 3.06ms astro:assets ->  astro:assets
2025-04-11T04:02:02.011Z vite:resolve 1.04ms /node_modules/astro/dist/assets/utils/resolveImports.js -> /root/meisici/shici001/node_modules/astro/dist/assets/utils/resolveImports.js
2025-04-11T04:02:02.011Z vite:resolve 1.20ms /node_modules/astro/dist/core/errors/index.js -> /root/meisici/shici001/node_modules/astro/dist/core/errors/index.js
2025-04-11T04:02:02.011Z vite:resolve 1.28ms /node_modules/astro/dist/core/path.js -> /root/meisici/shici001/node_modules/astro/dist/core/path.js
2025-04-11T04:02:02.011Z vite:resolve 1.39ms /node_modules/astro/dist/runtime/server/index.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/index.js
2025-04-11T04:02:02.011Z vite:resolve 1.51ms /node_modules/astro/dist/content/consts.js -> /root/meisici/shici001/node_modules/astro/dist/content/consts.js
2025-04-11T04:02:02.011Z vite:resolve 1.59ms /node_modules/astro/dist/content/data-store.js -> /root/meisici/shici001/node_modules/astro/dist/content/data-store.js
2025-04-11T04:02:02.013Z vite:resolve 2.50ms  astro:asset-imports -> null
2025-04-11T04:02:02.013Z vite:resolve 2.72ms  astro:assets -> null
2025-04-11T04:02:02.013Z vite:resolve 2.81ms  astro:content-module-imports -> null
2025-04-11T04:02:02.014Z vite:import-analysis 15.41ms [9 imports rewritten] node_modules/astro/dist/content/runtime.js
2025-04-11T04:02:02.014Z vite:transform 80.33ms /node_modules/astro/dist/content/runtime.js
2025-04-11T04:02:02.018Z vite:load 128.00ms [fs] /node_modules/astro/zod.mjs
2025-04-11T04:02:02.019Z vite:import-analysis 0.13ms [0 imports rewritten] node_modules/astro/zod.mjs
2025-04-11T04:02:02.019Z vite:transform 0.76ms /node_modules/astro/zod.mjs
2025-04-11T04:02:02.147Z vite:deps Dependencies bundled in 531.88ms
2025-04-11T04:02:02.148Z vite:load 134.16ms [fs] /node_modules/astro/dist/assets/utils/resolveImports.js
2025-04-11T04:02:02.150Z vite:resolve 0.25ms ../../content/consts.js -> /root/meisici/shici001/node_modules/astro/dist/content/consts.js
2025-04-11T04:02:02.150Z vite:resolve 0.32ms ../../runtime/server/shorthash.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/shorthash.js
2025-04-11T04:02:02.150Z vite:resolve 0.36ms ../consts.js -> /root/meisici/shici001/node_modules/astro/dist/assets/consts.js
2025-04-11T04:02:02.151Z vite:resolve 0.27ms /node_modules/astro/dist/runtime/server/shorthash.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/shorthash.js
2025-04-11T04:02:02.151Z vite:resolve 0.33ms /node_modules/astro/dist/assets/consts.js -> /root/meisici/shici001/node_modules/astro/dist/assets/consts.js
2025-04-11T04:02:02.151Z vite:import-analysis 3.22ms [3 imports rewritten] node_modules/astro/dist/assets/utils/resolveImports.js
2025-04-11T04:02:02.151Z vite:transform 3.59ms /node_modules/astro/dist/assets/utils/resolveImports.js
2025-04-11T04:02:02.153Z vite:load 139.43ms [fs] /node_modules/astro/dist/core/errors/index.js
2025-04-11T04:02:02.154Z vite:resolve 0.50ms ./errors-data.js -> /root/meisici/shici001/node_modules/astro/dist/core/errors/errors-data.js
2025-04-11T04:02:02.154Z vite:resolve 0.56ms ./errors.js -> /root/meisici/shici001/node_modules/astro/dist/core/errors/errors.js
2025-04-11T04:02:02.154Z vite:resolve 0.59ms ./printer.js -> /root/meisici/shici001/node_modules/astro/dist/core/errors/printer.js
2025-04-11T04:02:02.154Z vite:resolve 0.61ms ./utils.js -> /root/meisici/shici001/node_modules/astro/dist/core/errors/utils.js
2025-04-11T04:02:02.154Z vite:resolve 0.63ms ./zod-error-map.js -> /root/meisici/shici001/node_modules/astro/dist/core/errors/zod-error-map.js
2025-04-11T04:02:02.155Z vite:resolve 0.62ms /node_modules/astro/dist/core/errors/errors-data.js -> /root/meisici/shici001/node_modules/astro/dist/core/errors/errors-data.js
2025-04-11T04:02:02.155Z vite:resolve 0.67ms /node_modules/astro/dist/core/errors/errors.js -> /root/meisici/shici001/node_modules/astro/dist/core/errors/errors.js
2025-04-11T04:02:02.155Z vite:resolve 0.70ms /node_modules/astro/dist/core/errors/printer.js -> /root/meisici/shici001/node_modules/astro/dist/core/errors/printer.js
2025-04-11T04:02:02.155Z vite:resolve 0.73ms /node_modules/astro/dist/core/errors/utils.js -> /root/meisici/shici001/node_modules/astro/dist/core/errors/utils.js
2025-04-11T04:02:02.155Z vite:resolve 0.75ms /node_modules/astro/dist/core/errors/zod-error-map.js -> /root/meisici/shici001/node_modules/astro/dist/core/errors/zod-error-map.js
2025-04-11T04:02:02.156Z vite:import-analysis 2.52ms [5 imports rewritten] node_modules/astro/dist/core/errors/index.js
2025-04-11T04:02:02.156Z vite:transform 2.90ms /node_modules/astro/dist/core/errors/index.js
2025-04-11T04:02:02.157Z vite:load 143.45ms [fs] /node_modules/astro/dist/core/path.js
2025-04-11T04:02:02.157Z vite:import-analysis 0.10ms [0 imports rewritten] node_modules/astro/dist/core/path.js
2025-04-11T04:02:02.157Z vite:transform 0.48ms /node_modules/astro/dist/core/path.js
2025-04-11T04:02:02.158Z vite:load 144.16ms [fs] /node_modules/astro/dist/runtime/server/index.js
2025-04-11T04:02:02.159Z vite:resolve 0.48ms ./astro-component.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/astro-component.js
2025-04-11T04:02:02.159Z vite:resolve 0.53ms ./astro-global.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/astro-global.js
2025-04-11T04:02:02.159Z vite:resolve 0.88ms ./endpoint.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/endpoint.js
2025-04-11T04:02:02.159Z vite:resolve 0.93ms ./escape.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/escape.js
2025-04-11T04:02:02.159Z vite:resolve 1.04ms ./jsx.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/jsx.js
2025-04-11T04:02:02.159Z vite:resolve 1.08ms ./render/index.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/index.js
2025-04-11T04:02:02.159Z vite:resolve 1.12ms ./transition.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/transition.js
2025-04-11T04:02:02.161Z vite:resolve 0.87ms /node_modules/astro/dist/runtime/server/astro-component.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/astro-component.js
2025-04-11T04:02:02.161Z vite:resolve 0.93ms /node_modules/astro/dist/runtime/server/astro-global.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/astro-global.js
2025-04-11T04:02:02.161Z vite:resolve 0.96ms /node_modules/astro/dist/runtime/server/endpoint.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/endpoint.js
2025-04-11T04:02:02.161Z vite:resolve 0.99ms /node_modules/astro/dist/runtime/server/escape.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/escape.js
2025-04-11T04:02:02.161Z vite:resolve 1.01ms /node_modules/astro/dist/runtime/server/jsx.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/jsx.js
2025-04-11T04:02:02.161Z vite:resolve 1.04ms /node_modules/astro/dist/runtime/server/render/index.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/index.js
2025-04-11T04:02:02.161Z vite:resolve 1.07ms /node_modules/astro/dist/runtime/server/transition.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/transition.js
2025-04-11T04:02:02.162Z vite:import-analysis 3.93ms [7 imports rewritten] node_modules/astro/dist/runtime/server/index.js
2025-04-11T04:02:02.162Z vite:transform 4.21ms /node_modules/astro/dist/runtime/server/index.js
2025-04-11T04:02:02.164Z vite:load 150.00ms [fs] /node_modules/astro/dist/content/consts.js
2025-04-11T04:02:02.164Z vite:import-analysis 0.10ms [no imports] node_modules/astro/dist/content/consts.js
2025-04-11T04:02:02.164Z vite:transform 0.44ms /node_modules/astro/dist/content/consts.js
2025-04-11T04:02:02.164Z vite:load 150.76ms [fs] /node_modules/astro/dist/content/data-store.js
2025-04-11T04:02:02.167Z vite:resolve 0.02ms astro:data-layer-content ->  astro:data-layer-content
2025-04-11T04:02:02.167Z vite:resolve 0.06ms  astro:data-layer-content -> null
2025-04-11T04:02:02.167Z vite:import-analysis 2.53ms [1 imports rewritten] node_modules/astro/dist/content/data-store.js
2025-04-11T04:02:02.167Z vite:transform 2.80ms /node_modules/astro/dist/content/data-store.js
2025-04-11T04:02:02.184Z vite:deps creating _metadata.json in /root/meisici/shici001/node_modules/.vite/deps_temp_f7e63c22
2025-04-11T04:02:02.185Z vite:deps renaming /root/meisici/shici001/node_modules/.vite/deps_temp_f7e63c22 to /root/meisici/shici001/node_modules/.vite/deps
2025-04-11T04:02:02.185Z vite:deps ✨ dependencies optimized
2025-04-11T04:02:02.186Z vite:load 33.71ms [fs] /node_modules/astro/dist/runtime/server/shorthash.js
2025-04-11T04:02:02.187Z vite:import-analysis 0.06ms [no imports] node_modules/astro/dist/runtime/server/shorthash.js
2025-04-11T04:02:02.187Z vite:transform 0.50ms /node_modules/astro/dist/runtime/server/shorthash.js
2025-04-11T04:02:02.187Z vite:load 34.66ms [fs] /node_modules/astro/dist/assets/consts.js
2025-04-11T04:02:02.188Z vite:import-analysis 0.04ms [no imports] node_modules/astro/dist/assets/consts.js
2025-04-11T04:02:02.188Z vite:transform 0.46ms /node_modules/astro/dist/assets/consts.js
2025-04-11T04:02:02.188Z vite:load 31.55ms [fs] /node_modules/astro/dist/core/errors/errors-data.js
2025-04-11T04:02:02.201Z vite:import-analysis 1.19ms [no imports] node_modules/astro/dist/core/errors/errors-data.js
2025-04-11T04:02:02.201Z vite:transform 13.13ms /node_modules/astro/dist/core/errors/errors-data.js
2025-04-11T04:02:02.202Z vite:load 45.20ms [fs] /node_modules/astro/dist/core/errors/errors.js
2025-04-11T04:02:02.205Z vite:import-analysis 0.61ms [1 imports rewritten] node_modules/astro/dist/core/errors/errors.js
2025-04-11T04:02:02.205Z vite:transform 1.17ms /node_modules/astro/dist/core/errors/errors.js
2025-04-11T04:02:02.206Z vite:load 49.18ms [fs] /node_modules/astro/dist/core/errors/printer.js
2025-04-11T04:02:02.207Z vite:import-analysis 0.69ms [1 imports rewritten] node_modules/astro/dist/core/errors/printer.js
2025-04-11T04:02:02.207Z vite:transform 0.99ms /node_modules/astro/dist/core/errors/printer.js
2025-04-11T04:02:02.207Z vite:load 50.64ms [fs] /node_modules/astro/dist/core/errors/utils.js
2025-04-11T04:02:02.208Z vite:import-analysis 0.10ms [no imports] node_modules/astro/dist/core/errors/utils.js
2025-04-11T04:02:02.208Z vite:transform 0.78ms /node_modules/astro/dist/core/errors/utils.js
2025-04-11T04:02:02.209Z vite:load 52.61ms [fs] /node_modules/astro/dist/core/errors/zod-error-map.js
2025-04-11T04:02:02.210Z vite:import-analysis 0.06ms [no imports] node_modules/astro/dist/core/errors/zod-error-map.js
2025-04-11T04:02:02.210Z vite:transform 0.39ms /node_modules/astro/dist/core/errors/zod-error-map.js
2025-04-11T04:02:02.210Z vite:load 47.13ms [fs] /node_modules/astro/dist/runtime/server/astro-component.js
2025-04-11T04:02:02.211Z vite:resolve 0.15ms ../../core/errors/index.js -> /root/meisici/shici001/node_modules/astro/dist/core/errors/index.js
2025-04-11T04:02:02.211Z vite:cache [memory] /node_modules/astro/dist/core/errors/index.js
2025-04-11T04:02:02.211Z vite:import-analysis 0.75ms [1 imports rewritten] node_modules/astro/dist/runtime/server/astro-component.js
2025-04-11T04:02:02.211Z vite:transform 1.03ms /node_modules/astro/dist/runtime/server/astro-component.js
2025-04-11T04:02:02.212Z vite:load 48.55ms [fs] /node_modules/astro/dist/runtime/server/astro-global.js
2025-04-11T04:02:02.212Z vite:resolve 0.23ms ../../core/constants.js -> /root/meisici/shici001/node_modules/astro/dist/core/constants.js
2025-04-11T04:02:02.213Z vite:cache [memory] /node_modules/astro/dist/core/errors/index.js
2025-04-11T04:02:02.213Z vite:resolve 0.19ms /node_modules/astro/dist/core/constants.js -> /root/meisici/shici001/node_modules/astro/dist/core/constants.js
2025-04-11T04:02:02.213Z vite:import-analysis 1.41ms [2 imports rewritten] node_modules/astro/dist/runtime/server/astro-global.js
2025-04-11T04:02:02.214Z vite:transform 1.91ms /node_modules/astro/dist/runtime/server/astro-global.js
2025-04-11T04:02:02.214Z vite:load 51.18ms [fs] /node_modules/astro/dist/runtime/server/endpoint.js
2025-04-11T04:02:02.216Z vite:resolve 0.25ms ../../core/errors/errors-data.js -> /root/meisici/shici001/node_modules/astro/dist/core/errors/errors-data.js
2025-04-11T04:02:02.216Z vite:resolve 0.30ms ../../core/errors/errors.js -> /root/meisici/shici001/node_modules/astro/dist/core/errors/errors.js
2025-04-11T04:02:02.216Z vite:import-analysis 2.02ms [3 imports rewritten] node_modules/astro/dist/runtime/server/endpoint.js
2025-04-11T04:02:02.217Z vite:transform 2.30ms /node_modules/astro/dist/runtime/server/endpoint.js
2025-04-11T04:02:02.217Z vite:load 53.95ms [fs] /node_modules/astro/dist/runtime/server/escape.js
2025-04-11T04:02:02.219Z vite:resolve 0.17ms ./util.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/util.js
2025-04-11T04:02:02.219Z vite:resolve 0.20ms /node_modules/astro/dist/runtime/server/util.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/util.js
2025-04-11T04:02:02.219Z vite:import-analysis 1.95ms [1 imports rewritten] node_modules/astro/dist/runtime/server/escape.js
2025-04-11T04:02:02.219Z vite:transform 2.21ms /node_modules/astro/dist/runtime/server/escape.js
2025-04-11T04:02:02.220Z vite:load 57.02ms [fs] /node_modules/astro/dist/runtime/server/jsx.js
2025-04-11T04:02:02.221Z vite:resolve 0.30ms ../../jsx-runtime/index.js -> /root/meisici/shici001/node_modules/astro/dist/jsx-runtime/index.js
2025-04-11T04:02:02.221Z vite:resolve 0.36ms ./index.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/index.js
2025-04-11T04:02:02.221Z vite:resolve 0.39ms ./render/component.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/component.js
2025-04-11T04:02:02.221Z vite:cache [memory] /node_modules/astro/dist/runtime/server/index.js
2025-04-11T04:02:02.222Z vite:resolve 0.35ms /node_modules/astro/dist/jsx-runtime/index.js -> /root/meisici/shici001/node_modules/astro/dist/jsx-runtime/index.js
2025-04-11T04:02:02.222Z vite:resolve 0.41ms /node_modules/astro/dist/runtime/server/render/component.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/component.js
2025-04-11T04:02:02.222Z vite:import-analysis 1.61ms [3 imports rewritten] node_modules/astro/dist/runtime/server/jsx.js
2025-04-11T04:02:02.222Z vite:transform 1.89ms /node_modules/astro/dist/runtime/server/jsx.js
2025-04-11T04:02:02.223Z vite:load 60.33ms [fs] /node_modules/astro/dist/runtime/server/render/index.js
2025-04-11T04:02:02.225Z vite:resolve 0.84ms ./astro/index.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/astro/index.js
2025-04-11T04:02:02.225Z vite:resolve 0.91ms ./common.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/common.js
2025-04-11T04:02:02.225Z vite:resolve 0.95ms ./component.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/component.js
2025-04-11T04:02:02.225Z vite:resolve 0.98ms ./script.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/script.js
2025-04-11T04:02:02.225Z vite:resolve 1.00ms ./dom.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/dom.js
2025-04-11T04:02:02.225Z vite:resolve 1.03ms ./head.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/head.js
2025-04-11T04:02:02.225Z vite:resolve 1.05ms ./page.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/page.js
2025-04-11T04:02:02.225Z vite:resolve 1.07ms ./slot.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/slot.js
2025-04-11T04:02:02.225Z vite:resolve 1.09ms ./tags.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/tags.js
2025-04-11T04:02:02.225Z vite:resolve 1.11ms ./util.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/util.js
2025-04-11T04:02:02.228Z vite:resolve 1.34ms /node_modules/astro/dist/runtime/server/render/astro/index.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/astro/index.js
2025-04-11T04:02:02.228Z vite:resolve 1.41ms /node_modules/astro/dist/runtime/server/render/common.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/common.js
2025-04-11T04:02:02.228Z vite:resolve 1.44ms /node_modules/astro/dist/runtime/server/render/script.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/script.js
2025-04-11T04:02:02.228Z vite:resolve 1.46ms /node_modules/astro/dist/runtime/server/render/dom.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/dom.js
2025-04-11T04:02:02.228Z vite:resolve 1.48ms /node_modules/astro/dist/runtime/server/render/head.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/head.js
2025-04-11T04:02:02.228Z vite:resolve 1.50ms /node_modules/astro/dist/runtime/server/render/page.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/page.js
2025-04-11T04:02:02.228Z vite:resolve 1.52ms /node_modules/astro/dist/runtime/server/render/slot.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/slot.js
2025-04-11T04:02:02.228Z vite:resolve 1.54ms /node_modules/astro/dist/runtime/server/render/tags.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/tags.js
2025-04-11T04:02:02.228Z vite:resolve 1.56ms /node_modules/astro/dist/runtime/server/render/util.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/util.js
2025-04-11T04:02:02.228Z vite:import-analysis 4.51ms [10 imports rewritten] node_modules/astro/dist/runtime/server/render/index.js
2025-04-11T04:02:02.228Z vite:transform 4.97ms /node_modules/astro/dist/runtime/server/render/index.js
2025-04-11T04:02:02.231Z vite:load 67.64ms [fs] /node_modules/astro/dist/runtime/server/transition.js
2025-04-11T04:02:02.232Z vite:resolve 0.20ms ../../transitions/index.js -> /root/meisici/shici001/node_modules/astro/dist/transitions/index.js
2025-04-11T04:02:02.233Z vite:resolve 0.17ms /node_modules/astro/dist/transitions/index.js -> /root/meisici/shici001/node_modules/astro/dist/transitions/index.js
2025-04-11T04:02:02.233Z vite:import-analysis 2.10ms [2 imports rewritten] node_modules/astro/dist/runtime/server/transition.js
2025-04-11T04:02:02.233Z vite:transform 2.52ms /node_modules/astro/dist/runtime/server/transition.js
2025-04-11T04:02:02.312Z vite:load 97.52ms [fs] /node_modules/astro/dist/core/constants.js
2025-04-11T04:02:02.317Z vite:import-analysis 0.70ms [no imports] node_modules/astro/dist/core/constants.js
2025-04-11T04:02:02.317Z vite:transform 5.02ms /node_modules/astro/dist/core/constants.js
2025-04-11T04:02:02.318Z vite:load 97.78ms [fs] /node_modules/astro/dist/runtime/server/util.js
2025-04-11T04:02:02.319Z vite:import-analysis 0.29ms [no imports] node_modules/astro/dist/runtime/server/util.js
2025-04-11T04:02:02.319Z vite:transform 1.32ms /node_modules/astro/dist/runtime/server/util.js
2025-04-11T04:02:02.320Z vite:load 96.67ms [fs] /node_modules/astro/dist/jsx-runtime/index.js
2025-04-11T04:02:02.321Z vite:cache [memory] /node_modules/astro/dist/runtime/server/index.js
2025-04-11T04:02:02.322Z vite:import-analysis 1.36ms [1 imports rewritten] node_modules/astro/dist/jsx-runtime/index.js
2025-04-11T04:02:02.322Z vite:transform 1.75ms /node_modules/astro/dist/jsx-runtime/index.js
2025-04-11T04:02:02.323Z vite:load 99.70ms [fs] /node_modules/astro/dist/runtime/server/render/component.js
2025-04-11T04:02:02.329Z vite:resolve 1.71ms ./instruction.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/instruction.js
2025-04-11T04:02:02.329Z vite:resolve 2.03ms ../../../core/errors/index.js -> /root/meisici/shici001/node_modules/astro/dist/core/errors/index.js
2025-04-11T04:02:02.329Z vite:resolve 2.23ms ../escape.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/escape.js
2025-04-11T04:02:02.330Z vite:resolve 2.35ms ../hydration.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/hydration.js
2025-04-11T04:02:02.330Z vite:resolve 2.73ms ../serialize.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/serialize.js
2025-04-11T04:02:02.330Z vite:resolve 2.89ms ../shorthash.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/shorthash.js
2025-04-11T04:02:02.330Z vite:resolve 3.09ms ../util.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/util.js
2025-04-11T04:02:02.330Z vite:resolve 3.24ms ./astro/factory.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/astro/factory.js
2025-04-11T04:02:02.331Z vite:resolve 3.41ms ./astro/instance.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/astro/instance.js
2025-04-11T04:02:02.331Z vite:resolve 3.77ms ./server-islands.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/server-islands.js
2025-04-11T04:02:02.332Z vite:cache [memory] /node_modules/astro/dist/core/errors/index.js
2025-04-11T04:02:02.333Z vite:cache [memory] /node_modules/astro/dist/runtime/server/escape.js
2025-04-11T04:02:02.333Z vite:cache [memory] /node_modules/astro/dist/runtime/server/shorthash.js
2025-04-11T04:02:02.334Z vite:resolve 1.23ms /node_modules/astro/dist/runtime/server/render/instruction.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/instruction.js
2025-04-11T04:02:02.335Z vite:resolve 1.63ms /node_modules/astro/dist/runtime/server/hydration.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/hydration.js
2025-04-11T04:02:02.335Z vite:resolve 1.77ms /node_modules/astro/dist/runtime/server/serialize.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/serialize.js
2025-04-11T04:02:02.335Z vite:resolve 2.00ms /node_modules/astro/dist/runtime/server/render/astro/factory.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/astro/factory.js
2025-04-11T04:02:02.335Z vite:resolve 2.15ms /node_modules/astro/dist/runtime/server/render/astro/instance.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/astro/instance.js
2025-04-11T04:02:02.336Z vite:resolve 2.28ms /node_modules/astro/dist/runtime/server/render/server-islands.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/server-islands.js
2025-04-11T04:02:02.336Z vite:import-analysis 12.75ms [16 imports rewritten] node_modules/astro/dist/runtime/server/render/component.js
2025-04-11T04:02:02.337Z vite:transform 13.63ms /node_modules/astro/dist/runtime/server/render/component.js
2025-04-11T04:02:02.344Z vite:load 114.58ms [fs] /node_modules/astro/dist/runtime/server/render/astro/index.js
2025-04-11T04:02:02.346Z vite:resolve 0.56ms ./factory.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/astro/factory.js
2025-04-11T04:02:02.346Z vite:resolve 0.67ms ./head-and-content.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/astro/head-and-content.js
2025-04-11T04:02:02.346Z vite:resolve 0.77ms ./instance.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/astro/instance.js
2025-04-11T04:02:02.346Z vite:resolve 0.81ms ./render-template.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/astro/render-template.js
2025-04-11T04:02:02.346Z vite:resolve 0.84ms ./render.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/astro/render.js
2025-04-11T04:02:02.347Z vite:resolve 0.66ms /node_modules/astro/dist/runtime/server/render/astro/head-and-content.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/astro/head-and-content.js
2025-04-11T04:02:02.347Z vite:resolve 0.71ms /node_modules/astro/dist/runtime/server/render/astro/render-template.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/astro/render-template.js
2025-04-11T04:02:02.347Z vite:resolve 0.74ms /node_modules/astro/dist/runtime/server/render/astro/render.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/astro/render.js
2025-04-11T04:02:02.348Z vite:import-analysis 2.95ms [5 imports rewritten] node_modules/astro/dist/runtime/server/render/astro/index.js
2025-04-11T04:02:02.348Z vite:transform 3.51ms /node_modules/astro/dist/runtime/server/render/astro/index.js
2025-04-11T04:02:02.349Z vite:load 119.44ms [fs] /node_modules/astro/dist/runtime/server/render/common.js
2025-04-11T04:02:02.350Z vite:resolve 0.51ms ../scripts.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/scripts.js
2025-04-11T04:02:02.351Z vite:cache [memory] /node_modules/astro/dist/runtime/server/escape.js
2025-04-11T04:02:02.351Z vite:resolve 0.19ms /node_modules/astro/dist/runtime/server/scripts.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/scripts.js
2025-04-11T04:02:02.351Z vite:import-analysis 1.87ms [5 imports rewritten] node_modules/astro/dist/runtime/server/render/common.js
2025-04-11T04:02:02.351Z vite:transform 2.35ms /node_modules/astro/dist/runtime/server/render/common.js
2025-04-11T04:02:02.352Z vite:load 122.91ms [fs] /node_modules/astro/dist/runtime/server/render/script.js
2025-04-11T04:02:02.353Z vite:cache [memory] /node_modules/astro/dist/runtime/server/escape.js
2025-04-11T04:02:02.353Z vite:import-analysis 0.51ms [1 imports rewritten] node_modules/astro/dist/runtime/server/render/script.js
2025-04-11T04:02:02.354Z vite:transform 0.95ms /node_modules/astro/dist/runtime/server/render/script.js
2025-04-11T04:02:02.354Z vite:load 124.29ms [fs] /node_modules/astro/dist/runtime/server/render/dom.js
2025-04-11T04:02:02.355Z vite:cache [memory] /node_modules/astro/dist/runtime/server/escape.js
2025-04-11T04:02:02.355Z vite:import-analysis 0.86ms [3 imports rewritten] node_modules/astro/dist/runtime/server/render/dom.js
2025-04-11T04:02:02.355Z vite:transform 1.28ms /node_modules/astro/dist/runtime/server/render/dom.js
2025-04-11T04:02:02.356Z vite:load 126.20ms [fs] /node_modules/astro/dist/runtime/server/render/head.js
2025-04-11T04:02:02.357Z vite:cache [memory] /node_modules/astro/dist/runtime/server/escape.js
2025-04-11T04:02:02.357Z vite:import-analysis 0.95ms [3 imports rewritten] node_modules/astro/dist/runtime/server/render/head.js
2025-04-11T04:02:02.357Z vite:transform 1.36ms /node_modules/astro/dist/runtime/server/render/head.js
2025-04-11T04:02:02.358Z vite:load 128.10ms [fs] /node_modules/astro/dist/runtime/server/render/page.js
2025-04-11T04:02:02.359Z vite:resolve 0.56ms ./astro/render.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/astro/render.js
2025-04-11T04:02:02.359Z vite:import-analysis 1.36ms [5 imports rewritten] node_modules/astro/dist/runtime/server/render/page.js
2025-04-11T04:02:02.359Z vite:transform 1.66ms /node_modules/astro/dist/runtime/server/render/page.js
2025-04-11T04:02:02.360Z vite:load 130.20ms [fs] /node_modules/astro/dist/runtime/server/render/slot.js
2025-04-11T04:02:02.361Z vite:resolve 0.28ms ./astro/render-template.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/astro/render-template.js
2025-04-11T04:02:02.361Z vite:resolve 0.33ms ./any.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/any.js
2025-04-11T04:02:02.361Z vite:cache [memory] /node_modules/astro/dist/runtime/server/escape.js
2025-04-11T04:02:02.361Z vite:resolve 0.18ms /node_modules/astro/dist/runtime/server/render/any.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/any.js
2025-04-11T04:02:02.361Z vite:import-analysis 1.43ms [4 imports rewritten] node_modules/astro/dist/runtime/server/render/slot.js
2025-04-11T04:02:02.362Z vite:transform 1.66ms /node_modules/astro/dist/runtime/server/render/slot.js
2025-04-11T04:02:02.362Z vite:load 132.62ms [fs] /node_modules/astro/dist/runtime/server/render/tags.js
2025-04-11T04:02:02.363Z vite:import-analysis 0.34ms [1 imports rewritten] node_modules/astro/dist/runtime/server/render/tags.js
2025-04-11T04:02:02.363Z vite:transform 0.58ms /node_modules/astro/dist/runtime/server/render/tags.js
2025-04-11T04:02:02.363Z vite:load 133.53ms [fs] /node_modules/astro/dist/runtime/server/render/util.js
2025-04-11T04:02:02.364Z vite:cache [memory] /node_modules/astro/dist/runtime/server/escape.js
2025-04-11T04:02:02.364Z vite:import-analysis 0.40ms [1 imports rewritten] node_modules/astro/dist/runtime/server/render/util.js
2025-04-11T04:02:02.364Z vite:transform 0.64ms /node_modules/astro/dist/runtime/server/render/util.js
2025-04-11T04:02:02.365Z vite:load 130.21ms [fs] /node_modules/astro/dist/transitions/index.js
2025-04-11T04:02:02.365Z vite:resolve 0.12ms ../runtime/server/transition.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/transition.js
2025-04-11T04:02:02.365Z vite:cache [memory] /node_modules/astro/dist/runtime/server/transition.js
2025-04-11T04:02:02.365Z vite:import-analysis 0.47ms [1 imports rewritten] node_modules/astro/dist/transitions/index.js
2025-04-11T04:02:02.365Z vite:transform 0.68ms /node_modules/astro/dist/transitions/index.js
2025-04-11T04:02:02.407Z vite:load 67.12ms [fs] /node_modules/astro/dist/runtime/server/render/instruction.js
2025-04-11T04:02:02.407Z vite:import-analysis 0.11ms [no imports] node_modules/astro/dist/runtime/server/render/instruction.js
2025-04-11T04:02:02.407Z vite:transform 0.56ms /node_modules/astro/dist/runtime/server/render/instruction.js
2025-04-11T04:02:02.408Z vite:load 68.13ms [fs] /node_modules/astro/dist/runtime/server/hydration.js
2025-04-11T04:02:02.409Z vite:resolve 0.47ms ./serialize.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/serialize.js
2025-04-11T04:02:02.409Z vite:cache [memory] /node_modules/astro/dist/core/errors/index.js
2025-04-11T04:02:02.409Z vite:cache [memory] /node_modules/astro/dist/runtime/server/escape.js
2025-04-11T04:02:02.409Z vite:import-analysis 1.31ms [3 imports rewritten] node_modules/astro/dist/runtime/server/hydration.js
2025-04-11T04:02:02.410Z vite:transform 1.86ms /node_modules/astro/dist/runtime/server/hydration.js
2025-04-11T04:02:02.410Z vite:load 70.84ms [fs] /node_modules/astro/dist/runtime/server/serialize.js
2025-04-11T04:02:02.411Z vite:import-analysis 0.06ms [no imports] node_modules/astro/dist/runtime/server/serialize.js
2025-04-11T04:02:02.411Z vite:transform 0.41ms /node_modules/astro/dist/runtime/server/serialize.js
2025-04-11T04:02:02.412Z vite:load 72.00ms [fs] /node_modules/astro/dist/runtime/server/render/astro/factory.js
2025-04-11T04:02:02.412Z vite:import-analysis 0.03ms [no imports] node_modules/astro/dist/runtime/server/render/astro/factory.js
2025-04-11T04:02:02.412Z vite:transform 0.35ms /node_modules/astro/dist/runtime/server/render/astro/factory.js
2025-04-11T04:02:02.412Z vite:load 72.65ms [fs] /node_modules/astro/dist/runtime/server/render/astro/instance.js
2025-04-11T04:02:02.414Z vite:resolve 0.48ms ../../util.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/util.js
2025-04-11T04:02:02.414Z vite:resolve 0.50ms ../any.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/any.js
2025-04-11T04:02:02.414Z vite:cache [memory] /node_modules/astro/dist/runtime/server/util.js
2025-04-11T04:02:02.415Z vite:import-analysis 1.97ms [4 imports rewritten] node_modules/astro/dist/runtime/server/render/astro/instance.js
2025-04-11T04:02:02.415Z vite:transform 2.24ms /node_modules/astro/dist/runtime/server/render/astro/instance.js
2025-04-11T04:02:02.415Z vite:load 75.58ms [fs] /node_modules/astro/dist/runtime/server/render/server-islands.js
2025-04-11T04:02:02.417Z vite:resolve 0.41ms ../../../core/encryption.js -> /root/meisici/shici001/node_modules/astro/dist/core/encryption.js
2025-04-11T04:02:02.417Z vite:cache [memory] /node_modules/astro/dist/runtime/server/render/slot.js
2025-04-11T04:02:02.418Z vite:resolve 0.19ms /node_modules/astro/dist/core/encryption.js -> /root/meisici/shici001/node_modules/astro/dist/core/encryption.js
2025-04-11T04:02:02.418Z vite:import-analysis 2.41ms [3 imports rewritten] node_modules/astro/dist/runtime/server/render/server-islands.js
2025-04-11T04:02:02.418Z vite:transform 2.73ms /node_modules/astro/dist/runtime/server/render/server-islands.js
2025-04-11T04:02:02.419Z vite:load 70.46ms [fs] /node_modules/astro/dist/runtime/server/render/astro/head-and-content.js
2025-04-11T04:02:02.419Z vite:import-analysis 0.03ms [no imports] node_modules/astro/dist/runtime/server/render/astro/head-and-content.js
2025-04-11T04:02:02.419Z vite:transform 0.27ms /node_modules/astro/dist/runtime/server/render/astro/head-and-content.js
2025-04-11T04:02:02.420Z vite:load 71.11ms [fs] /node_modules/astro/dist/runtime/server/render/astro/render-template.js
2025-04-11T04:02:02.421Z vite:resolve 1.27ms ../../escape.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/escape.js
2025-04-11T04:02:02.421Z vite:resolve 1.34ms ../util.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/util.js
2025-04-11T04:02:02.422Z vite:cache [memory] /node_modules/astro/dist/runtime/server/escape.js
2025-04-11T04:02:02.422Z vite:cache [memory] /node_modules/astro/dist/runtime/server/util.js
2025-04-11T04:02:02.422Z vite:cache [memory] /node_modules/astro/dist/runtime/server/render/util.js
2025-04-11T04:02:02.422Z vite:import-analysis 2.17ms [4 imports rewritten] node_modules/astro/dist/runtime/server/render/astro/render-template.js
2025-04-11T04:02:02.422Z vite:transform 2.39ms /node_modules/astro/dist/runtime/server/render/astro/render-template.js
2025-04-11T04:02:02.423Z vite:load 74.02ms [fs] /node_modules/astro/dist/runtime/server/render/astro/render.js
2025-04-11T04:02:02.427Z vite:resolve 0.44ms ../../../../core/errors/index.js -> /root/meisici/shici001/node_modules/astro/dist/core/errors/index.js
2025-04-11T04:02:02.427Z vite:resolve 0.60ms ../common.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/render/common.js
2025-04-11T04:02:02.428Z vite:cache [memory] /node_modules/astro/dist/core/errors/index.js
2025-04-11T04:02:02.428Z vite:cache [memory] /node_modules/astro/dist/runtime/server/render/common.js
2025-04-11T04:02:02.428Z vite:cache [memory] /node_modules/astro/dist/runtime/server/render/util.js
2025-04-11T04:02:02.428Z vite:import-analysis 5.49ms [5 imports rewritten] node_modules/astro/dist/runtime/server/render/astro/render.js
2025-04-11T04:02:02.429Z vite:transform 5.83ms /node_modules/astro/dist/runtime/server/render/astro/render.js
2025-04-11T04:02:02.429Z vite:load 76.50ms [fs] /node_modules/astro/dist/runtime/server/scripts.js
2025-04-11T04:02:02.430Z vite:resolve 0.18ms ./astro-island.prebuilt-dev.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/astro-island.prebuilt-dev.js
2025-04-11T04:02:02.430Z vite:resolve 0.43ms ./astro-island.prebuilt.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/astro-island.prebuilt.js
2025-04-11T04:02:02.431Z vite:resolve 0.41ms /node_modules/astro/dist/runtime/server/astro-island.prebuilt-dev.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/astro-island.prebuilt-dev.js
2025-04-11T04:02:02.431Z vite:resolve 0.57ms /node_modules/astro/dist/runtime/server/astro-island.prebuilt.js -> /root/meisici/shici001/node_modules/astro/dist/runtime/server/astro-island.prebuilt.js
2025-04-11T04:02:02.431Z vite:import-analysis 1.95ms [2 imports rewritten] node_modules/astro/dist/runtime/server/scripts.js
2025-04-11T04:02:02.431Z vite:transform 2.26ms /node_modules/astro/dist/runtime/server/scripts.js
2025-04-11T04:02:02.432Z vite:load 70.02ms [fs] /node_modules/astro/dist/runtime/server/render/any.js
2025-04-11T04:02:02.433Z vite:cache [memory] /node_modules/astro/dist/runtime/server/escape.js
2025-04-11T04:02:02.434Z vite:cache [memory] /node_modules/astro/dist/runtime/server/util.js
2025-04-11T04:02:02.434Z vite:cache [memory] /node_modules/astro/dist/runtime/server/render/astro/index.js
2025-04-11T04:02:02.434Z vite:cache [memory] /node_modules/astro/dist/runtime/server/render/common.js
2025-04-11T04:02:02.434Z vite:cache [memory] /node_modules/astro/dist/runtime/server/render/slot.js
2025-04-11T04:02:02.434Z vite:cache [memory] /node_modules/astro/dist/runtime/server/render/util.js
2025-04-11T04:02:02.434Z vite:import-analysis 1.62ms [6 imports rewritten] node_modules/astro/dist/runtime/server/render/any.js
2025-04-11T04:02:02.434Z vite:transform 2.03ms /node_modules/astro/dist/runtime/server/render/any.js
2025-04-11T04:02:02.451Z vite:load 32.30ms [fs] /node_modules/astro/dist/core/encryption.js
2025-04-11T04:02:02.452Z vite:import-analysis 0.94ms [0 imports rewritten] node_modules/astro/dist/core/encryption.js
2025-04-11T04:02:02.452Z vite:transform 1.19ms /node_modules/astro/dist/core/encryption.js
2025-04-11T04:02:02.453Z vite:load 20.87ms [fs] /node_modules/astro/dist/runtime/server/astro-island.prebuilt-dev.js
2025-04-11T04:02:02.453Z vite:import-analysis 0.07ms [no imports] node_modules/astro/dist/runtime/server/astro-island.prebuilt-dev.js
2025-04-11T04:02:02.453Z vite:transform 0.32ms /node_modules/astro/dist/runtime/server/astro-island.prebuilt-dev.js
2025-04-11T04:02:02.453Z vite:load 21.45ms [fs] /node_modules/astro/dist/runtime/server/astro-island.prebuilt.js
2025-04-11T04:02:02.454Z vite:import-analysis 0.04ms [no imports] node_modules/astro/dist/runtime/server/astro-island.prebuilt.js
2025-04-11T04:02:02.454Z vite:transform 0.26ms /node_modules/astro/dist/runtime/server/astro-island.prebuilt.js
2025-04-11T04:02:02.469Z astro:content about collection added
2025-04-11T04:02:02.469Z astro:content pages collection added
2025-04-11T04:02:02.469Z astro:content sections collection added
12:02:02 [types] Generated 927ms
12:02:02 [build] output: "server"
12:02:02 [build] directory: /root/meisici/shici001/dist/
12:02:02 [build] adapter: @astrojs/node
12:02:02 [build] Collecting build info...
2025-04-11T04:02:02.478Z astro:build ├── ✔ node_modules/astro/dist/assets/endpoint/node.js
2025-04-11T04:02:02.478Z astro:build ├── ✔ src/pages/404.astro
2025-04-11T04:02:02.478Z astro:build ├── ✔ src/pages/500.astro
2025-04-11T04:02:02.479Z astro:build ├── ✔ src/pages/about.astro
2025-04-11T04:02:02.479Z astro:build ├── ✔ src/pages/ai/index.astro
2025-04-11T04:02:02.479Z astro:build ├── ✔ src/pages/api/pageview.ts
2025-04-11T04:02:02.479Z astro:build ├── ✔ src/pages/authors/index.astro
2025-04-11T04:02:02.479Z astro:build ├── ✔ src/pages/collections/index.astro
2025-04-11T04:02:02.479Z astro:build ├── ✔ src/pages/dynasties/index.astro
2025-04-11T04:02:02.479Z astro:build ├── ✔ src/pages/hot-works/index.astro
2025-04-11T04:02:02.479Z astro:build ├── ✔ src/pages/info.astro
2025-04-11T04:02:02.479Z astro:build ├── ✔ src/pages/sitemap-authors.xml.ts
2025-04-11T04:02:02.479Z astro:build ├── ✔ src/pages/sitemap-collections.xml.ts
2025-04-11T04:02:02.479Z astro:build ├── ✔ src/pages/sitemap-dynasties.xml.ts
2025-04-11T04:02:02.479Z astro:build ├── ✔ src/pages/sitemap-index.xml.ts
2025-04-11T04:02:02.479Z astro:build ├── ✔ src/pages/sitemap-pages.xml.ts
2025-04-11T04:02:02.479Z astro:build ├── ✔ src/pages/sitemap-works.xml.ts
2025-04-11T04:02:02.479Z astro:build ├── ✔ src/pages/today/index.astro
2025-04-11T04:02:02.480Z astro:build ├── ✔ src/pages/works/index.astro
2025-04-11T04:02:02.480Z astro:build ├── ✔ src/pages/index.astro
2025-04-11T04:02:02.480Z astro:build All pages loaded   1744344118.2s
12:02:02 [build] ✓ Completed in 1.03s.
12:02:02 [build] Building server entrypoints...
2025-04-11T04:02:02.498Z vite:config using resolved config: {
  configFile: undefined,
  cacheDir: '/root/meisici/shici001/node_modules/.vite',
  clearScreen: false,
  customLogger: {
    hasWarned: false,
    info: [Function: info],
    warn: [Function: warn],
    warnOnce: [Function: warnOnce],
    error: [Function: error],
    clearScreen: [Function: clearScreen],
    hasErrorLogged: [Function: hasErrorLogged]
  },
  appType: 'custom',
  optimizeDeps: {
    holdUntilCrawlEnd: true,
    entries: [
      '/root/meisici/shici001/src/**/*.{jsx,tsx,vue,svelte,html,astro}'
    ],
    exclude: [ 'astro', 'node-fetch', '@astrojs/react/server.js' ],
    include: [
      '@astrojs/react/client.js',
      'react',
      'react/jsx-runtime',
      'react/jsx-dev-runtime',
      'react-dom',
      'react',
      'react-dom',
      'react/jsx-dev-runtime',
      'react/jsx-runtime',
      'astro > cssesc',
      'astro > aria-query',
      'astro > axobject-query'
    ],
    esbuildOptions: { preserveSymlinks: false, jsx: 'automatic' }
  },
  plugins: [
    'vite:build-metadata',
    'vite:watch-package-data',
    'vite:pre-alias',
    'alias',
    'astro:build',
    'astro:markdown',
    '@mdx-js/rollup',
    'astro:jsx',
    'astro-content-virtual-mod-plugin',
    'astro:content-asset-propagation',
    'astro:assets:esm',
    'astro:vite-plugin-file-url',
    'astro:i18n',
    'astro:actions',
    'astro:container',
    'vite:react-babel',
    'vite:react-refresh',
    'vite:resolve',
    'vite:html-inline-proxy',
    'vite:css',
    'vite:esbuild',
    'vite:json',
    'vite:wasm-helper',
    'vite:worker',
    'vite:asset',
    '@astro/rollup-plugin-astro-analyzer',
    '@astro/plugin-build-internals',
    '@astro/plugin-renderers',
    '@astro/plugin-middleware-build',
    '@astro/plugin-build-pages',
    'astro:rollup-plugin-build-css',
    'astro:rollup-plugin-css-scope-to',
    'astro:head-metadata-build',
    'astro:rollup-plugin-prerender',
    'astro:chunks',
    'astro:build:normal',
    'astro:scripts',
    'astro:html',
    'astro:postprocess',
    'astro:integration-container',
    'astro:content-imports',
    '@astro/plugin-middleware',
    'astro:assets',
    'astro:prefetch',
    'astro:transitions',
    'astro:dev-toolbar',
    '@astro/plugin-actions',
    '@astrojs/react:opts',
    '@astrojs/mdx-postprocess',
    'astro-icon',
    'vite:wasm-fallback',
    'astro:vite-plugin-env',
    'vite:define',
    'vite:css-post',
    'vite:build-html',
    'vite:worker-import-meta-url',
    'vite:asset-import-meta-url',
    'vite:force-systemjs-wrap-complete',
    'commonjs',
    'vite:data-uri',
    'vite:dynamic-import-vars',
    'vite:import-glob',
    '@astro/plugin-build-manifest',
    'astro:rollup-plugin-single-css',
    'astro:rollup-plugin-inline-stylesheets',
    '@astrojs/vite-plugin-astro-ssr-server',
    '@astrojs/vite-plugin-astro-adapter',
    '@astrojs/vite-plugin-astro-adapter',
    'astro:tsconfig-alias',
    'astro:scripts:page-ssr',
    'astro:scanner',
    '@astrojs/vite-plugin-astro-ssr-manifest',
    'vite:build-import-analysis',
    'vite:esbuild-transpile',
    'vite:reporter',
    'vite:load-fallback'
  ],
  publicDir: '/root/meisici/shici001/public',
  root: '/root/meisici/shici001',
  envPrefix: 'PUBLIC_',
  define: {
    'import.meta.env.SITE': '"https://mokk.cn"',
    'import.meta.env.BASE_URL': '"/"',
    'import.meta.env.ASSETS_PREFIX': 'undefined',
    __ASTRO_INTERNAL_I18N_CONFIG__: '{"base":"/","format":"directory","site":"https://mokk.cn","trailingSlash":"never","isBuild":true}'
  },
  server: {
    preTransformRequests: true,
    hmr: false,
    watch: { ignored: [Array] },
    middlewareMode: true,
    sourcemapIgnoreList: [Function: isInNodeModules$1],
    fs: {
      strict: true,
      allow: [Array],
      deny: [Array],
      cachedChecks: undefined
    }
  },
  resolve: {
    mainFields: [ 'browser', 'module', 'jsnext:main', 'jsnext' ],
    conditions: [ 'astro' ],
    extensions: [
      '.mjs',  '.js',
      '.mts',  '.ts',
      '.jsx',  '.tsx',
      '.json'
    ],
    dedupe: [
      'astro',
      'react',
      'react-dom',
      'react-dom/server',
      'react',
      'react-dom'
    ],
    preserveSymlinks: false,
    alias: [
      [Object], [Object],
      [Object], [Object],
      [Object], [Object],
      [Object]
    ]
  },
  ssr: {
    target: 'node',
    noExternal: [
      'astro',
      'astro/components',
      '@nanostores/preact',
      '@fontsource/*',
      '@astrojs/mdx',
      '@astrojs/node',
      '@astrojs/partytown',
      '@astrojs/react',
      '@astrojs/sitemap',
      '@astrojs/tailwind',
      '@astrojs/solid-js',
      'astro-auto-import',
      'astro-font',
      'astro-icon',
      'astro-loading-indicator',
      '@mui/material',
      '@mui/base',
      '@babel/runtime',
      'use-immer',
      '@material-tailwind/react',
      '@astrojs/node',
      'astro'
    ],
    external: [ 'react-dom/server', 'react-dom/client', 'sharp' ],
    optimizeDeps: { noDiscovery: true, esbuildOptions: [Object] }
  },
  build: {
    target: 'esnext',
    cssTarget: 'esnext',
    outDir: '/root/meisici/shici001/dist/server/',
    assetsDir: '_astro',
    assetsInlineLimit: 4096,
    cssCodeSplit: true,
    sourcemap: false,
    rollupOptions: {
      preserveEntrySignatures: 'exports-only',
      input: [],
      output: [Object],
      onwarn: [Function: onwarn]
    },
    minify: false,
    terserOptions: {},
    write: true,
    emptyOutDir: false,
    copyPublicDir: false,
    manifest: false,
    lib: false,
    ssr: true,
    ssrManifest: false,
    ssrEmitAssets: true,
    reportCompressedSize: false,
    chunkSizeWarningLimit: 500,
    watch: null,
    cssMinify: true,
    modulePreload: { polyfill: false },
    commonjsOptions: { include: [Array], extensions: [Array] },
    dynamicImportVarsOptions: { warnOnError: true, exclude: [Array] }
  },
  css: {
    postcss: {
      cwd: '/root/meisici/shici001',
      env: 'production',
      plugins: [Array]
    },
    lightningcss: undefined
  },
  mode: 'production',
  logLevel: 'error',
  base: '/',
  esbuild: { jsxDev: false, jsx: 'automatic', jsxImportSource: undefined },
  configFileDependencies: [],
  inlineConfig: {
    configFile: false,
    cacheDir: '/root/meisici/shici001/node_modules/.vite/',
    clearScreen: false,
    customLogger: {
      hasWarned: false,
      info: [Function: info],
      warn: [Function: warn],
      warnOnce: [Function: warnOnce],
      error: [Function: error],
      clearScreen: [Function: clearScreen],
      hasErrorLogged: [Function: hasErrorLogged]
    },
    appType: 'custom',
    optimizeDeps: { entries: [Array], exclude: [Array], include: [Array] },
    plugins: [
      [Object], [Object], [Object],  [Object],
      [Object], [Object], [Array],   [Object],
      [Object], [Array],  [Array],   [Object],
      [Object], false,    [Array],   [Object],
      false,    [Object], undefined, [Object],
      [Object], [Object], [Object],  [Object],
      [Object], [Object], [Object],  [Object],
      [Array],  [Object], [Object],  [Object],
      [Array],  [Object], [Object],  [Object],
      [Object], [Object], [Object],  [Object],
      false,    [Object], [Object],  [Object],
      [Object], [Object], [Object],  [Object]
    ],
    publicDir: '/root/meisici/shici001/public/',
    root: '/root/meisici/shici001/',
    envPrefix: 'PUBLIC_',
    define: {
      'import.meta.env.SITE': '"https://mokk.cn"',
      'import.meta.env.BASE_URL': '"/"',
      'import.meta.env.ASSETS_PREFIX': 'undefined'
    },
    server: { hmr: false, watch: [Object], middlewareMode: true },
    resolve: { alias: [Array], conditions: [Array], dedupe: [Array] },
    ssr: { noExternal: [Array], external: [Array] },
    build: {
      target: 'esnext',
      cssMinify: true,
      assetsDir: '_astro',
      emptyOutDir: false,
      manifest: false,
      outDir: '/root/meisici/shici001/dist/server/',
      copyPublicDir: false,
      rollupOptions: [Object],
      ssr: true,
      ssrEmitAssets: true,
      minify: false,
      modulePreload: [Object],
      reportCompressedSize: false
    },
    css: { postcss: [Object] },
    mode: 'production',
    logLevel: 'error',
    base: '/'
  },
  decodedBase: '/',
  rawBase: '/',
  command: 'build',
  isWorker: false,
  mainConfig: null,
  bundleChain: [],
  isProduction: true,
  preview: {
    port: undefined,
    strictPort: undefined,
    host: undefined,
    allowedHosts: undefined,
    https: undefined,
    open: undefined,
    proxy: undefined,
    cors: undefined,
    headers: undefined
  },
  envDir: '/root/meisici/shici001',
  env: {
    PUBLIC_BASE_URL: 'https://mokk.cn',
    BASE_URL: '/',
    MODE: 'production',
    DEV: false,
    PROD: true
  },
  assetsInclude: [Function: assetsInclude],
  logger: {
    hasWarned: false,
    info: [Function: info],
    warn: [Function: warn],
    warnOnce: [Function: warnOnce],
    error: [Function: error],
    clearScreen: [Function: clearScreen],
    hasErrorLogged: [Function: hasErrorLogged]
  },
  packageCache: Map(1) {
    'fnpd_/root/meisici/shici001' => {
      dir: '/root/meisici/shici001',
      data: [Object],
      hasSideEffects: [Function: hasSideEffects],
      webResolvedImports: {},
      nodeResolvedImports: {},
      setResolvedCache: [Function: setResolvedCache],
      getResolvedCache: [Function: getResolvedCache]
    },
    set: [Function (anonymous)]
  },
  createResolver: [Function (anonymous)],
  worker: { format: 'iife', plugins: '() => plugins', rollupOptions: {} },
  experimental: { importGlobRestoreExtension: false, hmrPartialAccept: false },
  webSocketToken: 'HnbD2PphejKn',
  additionalAllowedHosts: [],
  getSortedPlugins: [Function: getSortedPlugins],
  getSortedPluginHooks: [Function: getSortedPluginHooks]
}
DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

DEPRECATION WARNING [import]: Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

  ╷
6 │   @import "base";
  │           ^^^^^^
  ╵
    src/styles/main.scss 6:11  root stylesheet

DEPRECATION WARNING [import]: Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

  ╷
7 │   @import "fonts";
  │           ^^^^^^^
  ╵
    src/styles/main.scss 7:11  root stylesheet

DEPRECATION WARNING [import]: Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

   ╷
11 │   @import "header";
   │           ^^^^^^^^
   ╵
    src/styles/main.scss 11:11  root stylesheet

DEPRECATION WARNING [import]: Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

   ╷
12 │   @import "components";
   │           ^^^^^^^^^^^^
   ╵
    src/styles/main.scss 12:11  root stylesheet

DEPRECATION WARNING [import]: Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

   ╷
13 │   @import "navigation";
   │           ^^^^^^^^^^^^
   ╵
    src/styles/main.scss 13:11  root stylesheet

WARNING: 3 repetitive deprecation warnings omitted.

12:02:08 [ERROR] [vite] x Build failed in 6.30s
2025-04-11T04:02:10.296Z astro:telemetry {
  context: {
    isGit: false,
    anonymousProjectId: 'b8c87cb47d46158dffa5c284c73d0cc2953984ddd37ee60a6696ba49833dcd74',
    packageManager: 'yarn',
    packageManagerVersion: '1.22.22',
    anonymousId: '5a5115b0b49fc0a18f573c291601b62b8ac07ac2964680bbea53da6ac01b6994',
    anonymousSessionId: 'b171f32c861da7e77e2391d1f61f000b71f330046f4fb3e8a60dd431641eada1'
  },
  meta: {
    nodeVersion: '18.20.8',
    viteVersion: '5.4.18',
    astroVersion: '4.16.18',
    systemPlatform: 'linux',
    systemRelease: '6.1.0-28-amd64',
    systemArchitecture: 'x64',
    cpuCount: 2,
    cpuModel: 'Intel(R) Xeon(R) Platinum 8255C CPU @ 2.50GHz',
    cpuSpeed: 2499,
    memoryInMb: 1966,
    isDocker: false,
    isTTY: undefined,
    isWSL: false,
    isCI: false,
    ciName: null
  }
}
2025-04-11T04:02:10.296Z astro:telemetry [
  {
    "eventName": "ASTRO_CLI_ERROR",
    "payload": {
      "name": "Error",
      "cliCommand": "build",
      "isFatal": true
    }
  }
]
[vite]: Rollup failed to resolve import "@/assets/writer.png" from "/root/meisici/shici001/src/pages/ai/index.astro".
This is most likely unintended because it can break your application at runtime.
If you do want to externalize this module explicitly add it to
`build.rollupOptions.external`
  Stack trace:
    at viteWarn (file:///root/meisici/shici001/node_modules/vite/dist/node/chunks/dep-DbT5NFX0.js:65789:17)
    at onRollupWarning (file:///root/meisici/shici001/node_modules/vite/dist/node/chunks/dep-DbT5NFX0.js:65819:5)
    at file:///root/meisici/shici001/node_modules/rollup/dist/es/shared/node-entry.js:20679:13
    at ModuleLoader.handleInvalidResolvedId (file:///root/meisici/shici001/node_modules/rollup/dist/es/shared/node-entry.js:21291:26)
error Command failed with exit code 1.
info Visit https://yarnpkg.com/en/docs/cli/run for documentation about this command.
