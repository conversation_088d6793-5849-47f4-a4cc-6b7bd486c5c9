{"extends": "astro/tsconfigs/strict", "compilerOptions": {"baseUrl": ".", "target": "es6", "allowJs": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "jsx": "react", "isolatedModules": true, "incremental": true, "allowSyntheticDefaultImports": true, "paths": {"@/components/*": ["src/components/*"], "@/helpers/*": ["./src/layouts/helpers/*"], "@/partials/*": ["./src/layouts/partials/*"], "@/*": ["./src/*"]}}, "include": ["**/*.ts", "**/*.tsx", "**/*.astro"], "exclude": ["node_modules"]}