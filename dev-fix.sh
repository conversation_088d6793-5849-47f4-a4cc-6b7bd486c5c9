#!/bin/bash

# 美诗词网站本地开发环境修复版启动脚本

echo "=== 美诗词网站本地开发环境 (修复版) ==="

# 安装依赖（如果node_modules不存在）
if [ ! -d "node_modules" ]; then
  echo "=== 安装依赖 ==="
  yarn install
fi

# 复制开发环境配置
if [ -f ".env.development" ]; then
  echo "=== 应用开发环境配置 ==="
  cp .env.development .env
fi

# 创建必要的文件
echo "=== 准备数据文件 ==="
node scripts/build_search.js
node scripts/build_date.js
node scripts/migrate_pageviews.js

# 设置环境变量
export NODE_ENV=development
export ASTRO_NODE_AUTOSTART=false

# 启动开发服务器
echo "=== 启动开发服务器 ==="
echo "网站将在 http://localhost:4321 启动"
npx astro dev 