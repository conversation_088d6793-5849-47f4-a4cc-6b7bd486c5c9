html {
  // @apply md:text-base;
}

body {
  @apply font-normal leading-relaxed bg-body dark:bg-darkmode-body text-text dark:text-darkmode-text;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  @apply font-bold leading-tight text-dark dark:text-darkmode-dark;
}

b,
strong {
  @apply font-semibold;
}

code {
  @apply after:border-none;
}

blockquote > p {
  @apply my-0 #{!important};
}

/* 添加靛蓝色链接样式 */
main {
  a:not(.btn, nav a, footer a, .navbar a, .header a, .animated-underline) {
    @apply text-[#065279] hover:text-[#043f5f] dark:text-[#1677b3] dark:hover:text-[#3498db];
  }
}

/* 修改animated-underline链接类 */
.animated-underline {
  @apply text-[#065279] hover:text-[#043f5f] dark:text-[#1677b3] dark:hover:text-[#3498db];
}
