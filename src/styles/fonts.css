/* 汇文明朝体字体定义 */
@font-face {
  font-family: 'HuiWenMingChao';
  src: url('/fonts/汇文明朝体.otf') format('opentype');
  font-weight: normal;
  font-style: normal;
  font-display: swap; /* 优化字体加载显示 */
}

/* 诗词正文使用明朝体的类 */
.poetry-content {
  font-family: 'HuiWenMingChao', serif;
  line-height: 1.8;
  font-size: 1.125rem; /* 18px */
  letter-spacing: 0.05em;
} 

/*
字体加载优化建议:
1. 在HTML的<head>中添加以下预加载指令:
   <link rel="preload" href="/fonts/汇文明朝体.otf" as="font" type="font/otf" crossorigin>

2. 可以考虑字体子集化:
   使用工具如fonttools或font-spider创建包含常用汉字的子集字体文件，减小字体文件大小

3. 可以考虑使用更现代的字体格式:
   将.otf转换为.woff2格式，体积可减小约30%，加载更快
   示例: src: url('/fonts/汇文明朝体.woff2') format('woff2'),
             url('/fonts/汇文明朝体.otf') format('opentype');

4. 如果需要在生产环境实施这些优化，请按照上述建议更新代码
*/ 