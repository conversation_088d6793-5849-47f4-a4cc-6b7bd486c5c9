#header.scroll>div:first-child {
    @apply bg-body md:bg-body/90 md:backdrop-blur-md;
    box-shadow: 0 0.375rem 1.5rem 0 rgb(140 152 164 / 13%);
  }
  
  .dark #header.scroll>div:first-child,
  #header.scroll.dark>div:first-child {
    @apply bg-darkmode-body border-b border-border/10;
    box-shadow: none;
  }
  
  #header.expanded nav {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    bottom: 70px !important;
    padding: 0 5px;
  }
  
  .dropdown:hover .dropdown-menu {
    display: block;
  }
  
  [astro-icon].icon-light>* {
    stroke-width: 1.2;
  }
  
  [astro-icon].icon-bold>* {
    stroke-width: 2.4;
  }
  
  [data-aw-toggle-menu] path {
    @apply transition;
  }
  
  [data-aw-toggle-menu].expanded g>path:first-child {
    @apply -rotate-45 translate-y-[15px] translate-x-[-3px];
  }
  
  [data-aw-toggle-menu].expanded g>path:last-child {
    @apply rotate-45 translate-y-[-8px] translate-x-[14px];
  }