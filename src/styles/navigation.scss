// navbar toggler
input#nav-toggle:checked + label #show-button {
  @apply hidden;
}

input#nav-toggle:checked + label #hide-button {
  @apply block;
}

input#nav-toggle:checked ~ #nav-menu {
  @apply block;
}

.header {
  @apply bg-body py-6 dark:bg-darkmode-body;
}

// navbar items
.navbar {
  @apply relative flex flex-wrap items-center justify-between;
}

.navbar-brand {
  @apply text-2xl font-bold text-dark dark:text-darkmode-dark;
  font-family: 'HuiWenMingChao', serif;
  letter-spacing: 0.05em;
  image {
    @apply max-h-full max-w-full;
  }
}

.navbar-nav {
  @apply text-center lg:text-left;
}

// .nav-item {
//   @apply mx-3;
// }

.nav-link {
  @apply text-dark hover:text-dark dark:text-darkmode-dark dark:hover:text-darkmode-primary block p-3 font-semibold transition lg:px-2 lg:py-3;
}

.nav-dropdown {
  @apply mr-0;
}

.nav-dropdown-list {
  @apply z-10 min-w-[180px] rounded bg-body p-4 shadow dark:bg-darkmode-body;
}

.nav-dropdown-item {
  @apply mb-2;
}

.nav-dropdown-link {
  @apply block py-1 font-semibold text-dark transition hover:text-dark dark:text-darkmode-text dark:hover:text-darkmode-primary;
}

//theme-switcher
.theme-switcher {
  @apply inline-flex;

  label {
    @apply relative inline-block h-4 w-6 cursor-pointer rounded-2xl bg-border lg:w-10;
  }

  input {
    @apply absolute opacity-0;
  }

  span {
    @apply absolute -top-1 left-0 flex h-6 w-6 items-center justify-center rounded-full bg-dark transition-all duration-300 dark:bg-white;
  }

  input:checked + label {
    span {
      @apply lg:left-4;
    }
  }
}
