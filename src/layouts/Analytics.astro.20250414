---
// 统计代码
// 包含百度统计和Google Analytics
---

<!-- 统计代码 -->
<!-- umami analytics cloud -->
<!-- please input your own website id -->
<script
  type="text/partytown"
  <script defer src="https://cloud.umami.is/script.js" 
  data-website-id="244bafdb-933b-41c7-acc7-7828a08613bd"></script>

<!-- Google tag (gtag.js) -->
 <!-- please input your own google analytics id -->
<!-- <script
  type="text/partytown"
  src="https://www.googletagmanager.com/gtag/js?id=G-WX3Y2HTFZ6"></script>
<script type="text/partytown">
  window.dataLayer = window.dataLayer || [];
  function gtag() {
    dataLayer.push(arguments);
  }
  gtag("js", new Date());
  gtag("config", "G-WX3Y2HTFZ6");
</script> -->

<!-- 百度统计 -->
<script>
var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?79c84b3cca520d2bbf78a040257a05e6";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script>

<script>
var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?908d4bbbbc4381a8dfdde8873a7f9e93";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script>



<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-WX3Y2HTFZ6"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-WX3Y2HTFZ6');
</script>
