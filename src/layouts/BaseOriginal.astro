import "@/styles/main.scss";

import TwSizeIndicator from "@/components/TwSizeIndicator.astro";
import BaseHead from "@/partials/BaseHead.astro";
import Footer from "@/partials/Footer.astro";
import Header from "@/partials/Header.astro";
import { footerConfig } from "@/config/FooterConfig";
import { headerConfig } from "@/config/HeaderConfig";
import SearchModal from "@/layouts/helpers/SearchModal";
import BasicScripts from "@/layouts/BasicScripts.astro";
import { Toaster } from "@/components/ui/sonner";
import ThemeSwitcher from "@/components/ThemeSwitcher.astro";

// types for frontmatters
export interface Props {
  title?: string;
  meta_title?: string;
  description?: string;
  image?: string;
  noindex?: boolean;
  canonical?: string;
  pageType?: string;
  entityId?: number;
}

// destructure frontmatters
const { 
  title, 
  meta_title, 
  description, 
  image, 
  noindex, 
  canonical,
  pageType = "other",
  entityId
} = Astro.props;

// 获取当前页面路径
const currentPath = Astro.url.pathname;

// console.log('Base meta:', title, meta_title, description, image, noindex, canonical);

<!doctype html>
<html lang="zh-CN">
  <head>
    <BaseHead
      title={title}
      meta_title={meta_title}
      description={description}
      image={image}
      noindex={noindex}
      canonical={canonical}
    />
    <script is:inline>
      // 页面加载时检查本地存储的主题季节设置
      const savedSeason = localStorage.getItem('preferred-theme-season');
      if (savedSeason) {
        document.documentElement.classList.add(`theme-${savedSeason}`);
      } else {
        // 根据月份设置默认季节
        const month = new Date().getMonth() + 1;
        let defaultSeason = 'spring';
        if (month >= 3 && month <= 5) defaultSeason = 'spring';
        else if (month >= 6 && month <= 8) defaultSeason = 'summer';
        else if (month >= 9 && month <= 11) defaultSeason = 'autumn';
        else defaultSeason = 'winter';
        
        document.documentElement.classList.add(`theme-${defaultSeason}`);
      }
    </script>
  </head>
  <body class="gradient-bg">
    <TwSizeIndicator />

    <!-- disable showRssFeed showSearch -->
    <Header
      {...headerConfig}
      isSticky
      showSearch
      showToggleTheme={false}
      showTwitter
      showGithub
    />

    <SearchModal client:visible />

    <main id="main-content">
      <slot />
    </main>

    <Footer {...footerConfig} />

    <!-- 波浪动画容器 -->
    <div class="wave-container fixed bottom-0 left-0 w-full h-16 z-0 opacity-60 pointer-events-none">
      <div class="wave"></div>
    </div>

    <ThemeSwitcher />

    <Toaster
      className="mt-14"
      position="top-right"
      richColors
      client:load
    />

    <BasicScripts />
    
    <!-- 季节主题脚本 -->
    <script src="/js/seasonal-theme.js"></script>
    
    <!-- 页面访问统计脚本 -->
    <script define:vars={{ currentPath, pageType, entityId }}>
      // 等待页面完全加载后记录访问
      document.addEventListener('DOMContentLoaded', async () => {
        try {
          const { trackPageView } = await import('/src/lib/pageViewTracker.js');
          trackPageView(currentPath, pageType, entityId);
        } catch (error) {
          console.error('Failed to track page view:', error);
        }
      });
    </script>
  </body>
</html>
