---
import "@/styles/main.scss";

import TwSizeIndicator from "@/components/TwSizeIndicator.astro";
import BaseHead from "@/partials/BaseHead.astro";
import Footer from "@/partials/Footer.astro";
import Header from "@/partials/Header.astro";
import { footerConfig } from "@/config/FooterConfig";
import { headerConfig } from "@/config/HeaderConfig";
import SearchModal from "@/layouts/helpers/SearchModal";
import BasicScripts from "@/layouts/BasicScripts.astro";
import { Toaster } from "@/components/ui/sonner";

// types for frontmatters
export interface Props {
  title?: string;
  meta_title?: string;
  description?: string;
  image?: string;
  noindex?: boolean;
  canonical?: string;
  pageType?: string;
  entityId?: number;
}

// destructure frontmatters
const { 
  title, 
  meta_title, 
  description, 
  image, 
  noindex, 
  canonical,
  pageType = "other",
  entityId
} = Astro.props;

// 获取当前页面路径
const currentPath = Astro.url.pathname;

// console.log('Base meta:', title, meta_title, description, image, noindex, canonical);
---

<!doctype html>
<html lang="zh-CN">
  <head>
<meta name="baidu-site-verification" content="codeva-Ll4AurGILk" />    <BaseHead
      title={title}
      meta_title={meta_title}
      description={description}
      image={image}
      noindex={noindex}
      canonical={canonical}
    />
  </head>
  <body>
    <TwSizeIndicator />

    <!-- disable showRssFeed showSearch -->
    <Header
      {...headerConfig}
      isSticky
      showSearch
      showToggleTheme={false}
      showTwitter
      showGithub
    />

    <SearchModal client:visible />

    <main id="main-content">
      <slot />
    </main>

    <Footer {...footerConfig} />

    <Toaster
      className="mt-14"
      position="top-right"
      richColors
      client:load
    />

    <BasicScripts />
    
    <!-- 页面访问统计脚本 -->
    <script define:vars={{ currentPath, pageType, entityId }}>
      // 等待页面完全加载后记录访问
      document.addEventListener('DOMContentLoaded', async () => {
        try {
          const { trackPageView } = await import('/src/lib/pageViewTracker.js');
          trackPageView(currentPath, pageType, entityId);
        } catch (error) {
          console.error('Failed to track page view:', error);
        }
      });
    </script>
  </body>
</html>
