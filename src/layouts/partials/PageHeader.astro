---
import Breadcrumbs from "@/components/Breadcrumbs.astro";

const { title = "", desc = "", pageType = "", showBreadcrumb = true }: 
  { title?: string; desc?: string; pageType?: string; showBreadcrumb?: boolean } = Astro.props;
---

<section>
  <div class="container text-center">
    <!-- bg-gradient-to-b from-body to-theme-light
        dark:from-darkmode-body dark:to-darkmode-theme-light -->
    <div class="rounded-2xl bg-gray-50 dark:bg-darkmode-theme-dark shadow-sm py-10 px-4 md:px-6">
      {showBreadcrumb && (
        <div class="text-left max-w-[800px] mx-auto mb-6">
          <Breadcrumbs pageType={pageType} pageTitle={title} />
        </div>
      )}
      <h1 class="text-3xl font-bold" set:text={title} />
      {desc && (
        <div class="mt-4 max-w-[800px] mx-auto text-gray-600 dark:text-gray-300 text-sm md:text-base">
          <p>{desc}</p>
        </div>
      )}
    </div>
  </div>
</section>
