---
import BackToTop from "@/components/BackToTop.astro";
import Logo from "@/components/Logo.astro";
import { Icon } from "astro-icon/components";

interface Link {
  text?: string;
  href?: string;
  ariaLabel?: string;
  icon?: string;
}

interface Links {
  title?: string;
  links: Array<Link>;
}

export interface Props {
  links: Array<Links>;
  secondaryLinks: Array<Link>;
  socialLinks: Array<Link>;
  footNote?: string;
  theme?: string;
}

const {
  socialLinks = [],
  secondaryLinks = [],
  links = [],
  footNote = "",
  theme = "light",
} = Astro.props;
---

<footer class:list={[{ dark: theme === 'dark' }, 'relative border-t border-gray-200 dark:border-gray-800 not-prose']}>
  
  <!-- Footer Background -->
  <div class="bg-theme-light dark:bg-darkmode-theme-light absolute inset-0 pointer-events-none" aria-hidden="true"></div>
  
  <!-- Footer Links -->
  <div class="relative max-w-7xl mx-auto px-6 sm:px-6 md:px-6 lg:px-6 xl:px-0 2xl:px-0">
    
    <!-- Footer Logo -->
    <div class="grid grid-cols-12 gap-4 gap-y-8 sm:gap-8 pt-8 md:pt-12">
      <div class="col-span-12 lg:col-span-4">
        <div class="mb-2">
          <Logo />
        </div>
        <div class="mt-6">
          {
            secondaryLinks.map(({ text, href }) => (
              <a
                class="animated-underline py-1 mr-2 text-sm"
                href={href}
                set:html={text}
              />
            ))
          }
        </div>
      </div>

      <!-- Footer Links -->
      {
        links.map(({ title, links }) => (
          <div class="lg:text-center col-span-6 md:col-span-3 lg:col-span-2">
            <h4 class="mb-4">{title}</h4>
            {links && Array.isArray(links) && links.length > 0 && (
              <ul>
                {links.map(({ text, href, ariaLabel }) => (
                  <li class="mb-2">
                    <a
                      class="animated-underline py-1 text-sm"
                      href={href}
                      aria-label={ariaLabel}
                      {...(href?.includes('/works/') ? {'data-astro-reload': ''} : {})}
                    >
                      <Fragment set:html={text} />
                    </a>
                  </li>
                ))}
              </ul>
            )}
          </div>
        ))
      }
    </div>

    <!-- Footer Copyright and extra links -->
    <div class="md:flex md:items-center md:justify-between py-6 md:pb-8 md:pt-6">
      {
        socialLinks?.length ? (
          <ul class="flex mb-4 md:order-1 -ml-2 md:ml-4 md:mb-0">
            {socialLinks.map(({ ariaLabel, href, text, icon }) => (
              <li>
                <a
                  class="rounded-lg text-sm p-2.5 inline-flex items-center hover:bg-gray-200 dark:hover:bg-gray-800"
                  aria-label={ariaLabel}
                  href={href}
                  target="_blank"
                >
                  {icon && <Icon name={icon} class="w-6 h-6 md:w-5 md:h-5 md:inline-block" />}
                  <Fragment set:html={text} />
                </a>
              </li>
            ))}
          </ul>
        ) : (
          ''
        )
      }

      <div class="text-sm mr-4">
        <Fragment set:html={footNote} />
      </div>
    </div>

    <!-- back to top button -->
    <BackToTop />
  </div>
</footer>
