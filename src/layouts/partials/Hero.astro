---
import heroImage from "@/assets/hero.webp";
import { Picture } from "astro:assets";
import { getEntry } from "astro:content";

const hero = await getEntry("sections", "hero");
---

{
  hero.data.enable && (
    <div class="grid items-center md:grid-cols-2">
      {/* hero image on the right */}
      <div class="py-6 hidden md:block md:order-1">
        <Picture
          loading="lazy"
          src={heroImage}
          alt="hero"
          widths={[200, 400, 600]}
          sizes="(max-width: 400px) 100vw, 400px"
          format="webp"
        />
      </div>

      {/* hero content on the left */}
      <div>
        <h1 class="text-2xl md:text-3xl lg:text-4xl xl:text-5xl lg:tracking-tight xl:tracking-tighter">
          {hero.data.title}
        </h1>
        <p class="text-lg mt-10">{hero.data.description}</p>

        {/* action buttons */}
        <div class="mt-10 flex flex-wrap justify-start gap-6">
          <a
            href={hero.data.primaryButton.link}
            class="relative flex h-12 items-center justify-center px-8 before:bg-dark dark:before:bg-darkmode-dark before:absolute before:inset-0 before:rounded-full before:transition before:duration-300 hover:before:scale-105 active:duration-75 active:before:scale-95 sm:w-max"
          >
            <span class="relative text-darkmode-dark dark:text-dark">
              {hero.data.primaryButton.label}
            </span>
          </a>
          <a
            href={hero.data.secondaryButton.link}
            class="relative flex h-12 items-center justify-center px-8 before:bg-primary/10 before:bg-gradient-to-b dark:before:border-gray-700 dark:before:bg-gray-800 before:absolute before:inset-0 before:rounded-full before:border before:border-transparent before:transition before:duration-300 hover:before:scale-105 active:duration-75 active:before:scale-95 sm:w-max"
          >
            <span class="relative text-dark dark:text-darkmode-dark">
              {hero.data.secondaryButton.label}
            </span>
          </a>
        </div>
      </div>
    </div>
  )
}
