---
import { Icon } from "astro-icon/components";
import { markdownify } from "@/lib/utils/textConverter";
import { getEntry } from "astro:content";

const features = await getEntry("sections", "features");
---

<div class="text-gray-600 dark:text-gray-300">
  <div class="mb-20 space-y-4 px-6 md:px-0">
    <h2 class="text-center font-bold text-2xl lg:text-4xl lg:tracking-tight">
      {markdownify(features.data.title)}
    </h2>
    <p class="text-center mt-4 text-gray-500 dark:text-gray-300">
      {markdownify(features.data.description)}
    </p>
  </div>

  <div class="grid sm:grid-cols-2 md:grid-cols-3 mt-16 gap-16">
    {
      features.data.features.map(
        (item: { icon: string; title: string; description: string, color: string }) => (
          <div class="flex gap-4 items-start">
            <div class:list={`mt-1 rounded-full p-2 w-8 h-8 shrink-0 ${item.color}`}>
              <Icon class="text-white" name={item.icon} />
            </div>
            <div class="mt-1">
              <h3 class="font-medium text-lg text-gray-700 dark:text-gray-200">
                {item.title}
              </h3>{" "}
              <p class="mt-2 leading-relaxed">{item.description}</p>
            </div>
          </div>
        ),
      )
    }
  </div>
</div>
