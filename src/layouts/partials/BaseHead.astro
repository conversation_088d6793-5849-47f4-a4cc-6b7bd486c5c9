---
import "@/styles/main.scss";

import config from "@/config/config.json";

import Analytics from "@/layouts/Analytics.astro";
import LoadingIndicator from "astro-loading-indicator/component";
import { ViewTransitions } from "astro:transitions";

// types for frontmatters
export interface Props {
  title?: string;
  meta_title?: string;
  description?: string;
  image?: string;
  noindex?: boolean;
  canonical?: string;
}

// destructure frontmatters
const { title, meta_title, description, image, noindex, canonical } =
  Astro.props;

const pageAuthor = config.metadata.meta_author;
const pageUrl = `${config.site.base_url}/${Astro.url.pathname.replace("/", "")}`;
const pageTitle = meta_title ? meta_title : title ? title : config.site.title;
const pageDescription = description ? description : config.metadata.meta_description;
const pageImage = `${config.site.base_url}${
  image ? image : config.metadata.meta_image
}`;

const isProd = import.meta.env.PROD;
---

<!-- favicon -->
<link rel="shortcut icon" href={config.site.favicon} />
<!-- theme meta -->
<meta name="color-scheme" content="light dark" />
<meta name="theme-name" content="meishici" />
<meta name="msapplication-TileColor" content="#000000" />
<meta name="theme-color" media="(prefers-color-scheme: light)" content="#fff" />
<meta name="theme-color" media="(prefers-color-scheme: dark)" content="#000" />
<meta name="generator" content={Astro.generator} />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />

<!-- responsive meta -->
<meta
  name="viewport"
  content="width=device-width, initial-scale=1, maximum-scale=5"
/>

<!-- 移动友好性设置 -->
<meta name="mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-status-bar-style" content="default" />

<!-- 浏览器缓存控制 -->
<meta http-equiv="Cache-Control" content="max-age=86400, public" />

<!-- DNS预取和资源预连接 -->
<link rel="dns-prefetch" href={config.site.base_url} />
<link rel="preconnect" href={config.site.base_url} crossorigin />

<!-- 预加载字体资源 -->
<link rel="preload" href="/fonts/汇文明朝体.otf" as="font" type="font/otf" crossorigin />

<!-- title -->
<title>
  {pageTitle}
</title>

<!-- GA -->
<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-WX3Y2HTFZ6"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-WX3Y2HTFZ6');
</script>

<!-- clarity -->

<script type="text/javascript">
    (function(c,l,a,r,i,t,y){
        c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
        t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
        y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
    })(window, document, "clarity", "script", "r3x22ljj3x");
</script>


<script async src="https://cdn.seline.com/seline.js" data-token="8bf7b676d959c7b"></script>

<!-- canonical url -->
{canonical && <link rel="canonical" href={canonical} item-prop="url" />}

<!-- noindex robots -->
{noindex && <meta name="robots" content="noindex,nofollow" />}

<!-- site map -->
<link rel="sitemap" href="/sitemap-index.xml" />

<!-- meta-description -->
<meta name="description" content={pageDescription} />

<!-- astro view transition -->
<ViewTransitions />
<LoadingIndicator color="#9333EA" />

<!-- author from config.json -->
<meta name="author" content={pageAuthor} />

<!-- og-title -->
<meta property="og:title" content={pageTitle} />

<!-- og-image -->
<meta property="og:image" content={pageImage} />

<!-- og-description -->
<meta property="og:description" content={pageDescription} />
<meta property="og:type" content="website" />
<meta property="og:url" content={pageUrl} />

<!-- twitter-title -->
<meta property="twitter:title" content={pageTitle} />
<!-- twitter-site -->
<meta property="twitter:site" content={pageUrl} />
<!-- twitter-description -->
<meta property="twitter:description" content={pageDescription} />
<!-- twitter-image -->
<meta property="twitter:image" content={pageImage} />
<meta property="twitter:card" content="summary_large_image" />

<!-- Analytics -->
{
  isProd && <Analytics />
}
