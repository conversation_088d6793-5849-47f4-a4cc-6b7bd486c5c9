import searchData from ".json/search.json";
import React, { useEffect, useState } from "react";
import SearchResult, { type ISearchItem } from "./SearchResult";

const SearchModal = () => {
  const [searchString, setSearchString] = useState("");
  const [inputString, setInputString] = useState("");

  // handle input change
  const handleSearch = (e: React.FormEvent<HTMLInputElement>) => {
    // setSearchString(e.currentTarget.value.replace("\\", "").toLowerCase());
    setInputString(e.currentTarget.value.replace("\\", "").toLowerCase());
  };

  // handle search on Enter key press
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      setSearchString(inputString);
    }
  };

  // generate search result
  const doSearch = (searchData: ISearchItem[]) => {
    // 转义特殊字符，避免正则表达式错误
    const escapedSearchString = searchString.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const regex = new RegExp(`${escapedSearchString}`, "gi");
    if (searchString === "") {
      return [];
    } else {
      const searchResult = searchData.filter((item) => {
        const title = item.frontmatter.title.toLowerCase().match(regex);
        const author = item.frontmatter.author
          ?.toLowerCase()
          .match(regex);
        const description = item.frontmatter.description
          ?.toLowerCase()
          .match(regex);
        const categories = item.frontmatter.categories
          ?.join(" ")
          .toLowerCase()
          .match(regex);
        const tags = item.frontmatter.tags
          ?.join(" ")
          .toLowerCase()
          .match(regex);
        const content = item.content.toLowerCase().match(regex);

        if (title || author || content || description || categories || tags) {
          return item;
        }
        return false; // 明确返回 false，避免 undefined 返回值
      });
      return searchResult;
    }
  };

  // get search result
  const startTime = performance.now();
  const searchResult = doSearch(searchData);
  const endTime = performance.now();
  const totalTime = ((endTime - startTime) / 1000).toFixed(3);

  // search dom manipulation
  useEffect(() => {
    const searchModal = document.getElementById("searchModal");
    const searchInput = document.getElementById("searchInput");
    const searchModalOverlay = document.getElementById("searchModalOverlay");
    const searchResultItems = document.querySelectorAll("#searchItem");
    const searchModalTriggers = document.querySelectorAll(
      "[data-search-trigger]",
    );
    
    // 重置选中索引，确保在搜索结果变化时重置导航
    let selectedIndex = -1;

    // search modal open
    searchModalTriggers.forEach((button) => {
      button.addEventListener("click", function () {
        const searchModal = document.getElementById("searchModal");
        searchModal?.classList.add("show");
        searchInput?.focus();
      });
    });

    // search modal close
    searchModalOverlay?.addEventListener("click", function () {
      searchModal?.classList.remove("show");
    });

    // keyboard navigation
    const updateSelection = () => {
      // 确保 selectedIndex 在有效范围内
      if (selectedIndex >= searchResultItems.length) {
        selectedIndex = searchResultItems.length - 1;
      }
      if (selectedIndex < -1) {
        selectedIndex = -1;
      }
      
      searchResultItems.forEach((item, index) => {
        if (index === selectedIndex) {
          item.classList.add("search-result-item-active");
        } else {
          item.classList.remove("search-result-item-active");
        }
      });

      if (selectedIndex >= 0 && selectedIndex < searchResultItems.length) {
        searchResultItems[selectedIndex]?.scrollIntoView({
          behavior: "smooth",
          block: "nearest",
        });
      }
    };

    // 创建键盘事件处理函数
    const handleKeyDown = function (event: KeyboardEvent) {
      if ((event.metaKey || event.ctrlKey) && event.key === "k") {
        searchModal?.classList.add("show");
        searchInput?.focus();
        updateSelection();
      }

      if (event.key === "ArrowUp" || event.key === "ArrowDown") {
        event.preventDefault();
      }

      if (event.key === "Escape") {
        searchModal?.classList.remove("show");
      }

      if (event.key === "ArrowUp" && selectedIndex > 0) {
        selectedIndex--;
      } else if (
        event.key === "ArrowDown" &&
        selectedIndex < searchResultItems.length - 1
      ) {
        selectedIndex++;
      } else if (event.key === "Enter") {
        const activeLink = document.querySelector(
          ".search-result-item-active a",
        ) as HTMLAnchorElement;
        if (activeLink) {
          activeLink.click();
        }
      }

      updateSelection();
    };

    // 添加键盘事件监听器
    document.addEventListener("keydown", handleKeyDown);

    // 清理函数，移除事件监听器
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [searchString, searchResult.length]);

  return (
    <div id="searchModal" className="search-modal">
      <div id="searchModalOverlay" className="search-modal-overlay" />
      <div className="search-wrapper">
        <div className="search-wrapper-header">
          <label
            htmlFor="searchInput"
            className="absolute left-7 top-[calc(50%-7px)]"
          >
            <span className="sr-only">search icon</span>
            {searchString ? (
              <svg
                onClick={() => setSearchString("")}
                viewBox="0 0 512 512"
                height="18"
                width="18"
                className="hover:text-red-500 cursor-pointer -mt-0.5"
              >
                <title>close icon</title>
                <path
                  fill="currentcolor"
                  d="M256 512A256 256 0 10256 0a256 256 0 100 512zM175 175c9.4-9.4 24.6-9.4 33.9.0l47 47 47-47c9.4-9.4 24.6-9.4 33.9.0s9.4 24.6.0 33.9l-47 47 47 47c9.4 9.4 9.4 24.6.0 33.9s-24.6 9.4-33.9.0l-47-47-47 47c-9.4 9.4-24.6 9.4-33.9.0s-9.4-24.6.0-33.9l47-47-47-47c-9.4-9.4-9.4-24.6.0-33.9z"
                ></path>
              </svg>
            ) : (
              <svg
                viewBox="0 0 512 512"
                height="18"
                width="18"
                className="-mt-0.5"
              >
                <title>search icon</title>
                <path
                  fill="currentcolor"
                  d="M416 208c0 45.9-14.9 88.3-40 122.7L502.6 457.4c12.5 12.5 12.5 32.8.0 45.3s-32.8 12.5-45.3.0L330.7 376c-34.4 25.2-76.8 40-122.7 40C93.1 416 0 322.9.0 208S93.1.0 208 0 416 93.1 416 208zM208 352a144 144 0 100-288 144 144 0 100 288z"
                ></path>
              </svg>
            )}
          </label>
          <input
            id="searchInput"
            placeholder="请输入搜索内容，并按回车键开始搜索..."
            className="search-wrapper-header-input"
            type="input"
            name="search"
            value={inputString}
            onChange={handleSearch}
            onKeyDown={handleKeyDown}
            autoComplete="off"
          />
        </div>
        <SearchResult searchResult={searchResult} searchString={searchString} />
        <div className="search-wrapper-footer">
          <span className="flex items-center">
            <kbd>
              <svg
                width="14"
                height="14"
                fill="currentcolor"
                viewBox="0 0 16 16"
              >
                <path d="M3.204 11h9.592L8 5.519 3.204 11zm-.753-.659 4.796-5.48a1 1 0 011.506.0l4.796 5.48c.566.647.106 1.659-.753 1.659H3.204a1 1 0 01-.753-1.659z"></path>
              </svg>
            </kbd>
            <kbd>
              <svg
                width="14"
                height="14"
                fill="currentcolor"
                viewBox="0 0 16 16"
              >
                <path d="M3.204 5h9.592L8 10.481 3.204 5zm-.753.659 4.796 5.48a1 1 0 001.506.0l4.796-5.48c.566-.647.106-1.659-.753-1.659H3.204a1 1 0 00-.753 1.659z"></path>
              </svg>
            </kbd>
            {/* to navigate */}
            上下导航
          </span>
          <span className="flex items-center">
            <kbd>
              <svg
                width="12"
                height="12"
                fill="currentcolor"
                viewBox="0 0 16 16"
              >
                <path
                  fillRule="evenodd"
                  d="M14.5 1.5a.5.5.0 01.5.5v4.8a2.5 2.5.0 01-2.5 2.5H2.707l3.347 3.346a.5.5.0 01-.708.708l-4.2-4.2a.5.5.0 010-.708l4-4a.5.5.0 11.708.708L2.707 8.3H12.5A1.5 1.5.0 0014 6.8V2a.5.5.0 01.5-.5z"
                ></path>
              </svg>
            </kbd>
            {/* to select */}
            进入条目
          </span>
          <span>
            <kbd>CMD/CTRL+K</kbd> {/* to close */}打开搜索
          </span>
          <span>
            <kbd>ESC</kbd> {/* to close */}关闭搜索
          </span>

          {searchString && (
            <span>
              {/* <strong>{searchResult.length} </strong> results - in{" "}
              <strong>{totalTime} </strong> seconds */}
              <strong>{searchResult.length} </strong> 结果 - {" "}
              <strong>{totalTime} </strong> 秒
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

export default SearchModal;
