---
import type { ImageMetadata } from "astro";
import { Image } from "astro:assets";

// Props interface for the component
interface Props {
  src: string;
  alt: string;
  width: number;
  height: number;
  loading?: "eager" | "lazy" | null | undefined;
  decoding?: "async" | "auto" | "sync" | null | undefined;
  format?: "auto" | "avif" | "jpeg" | "png" | "svg" | "webp";
  class?: string;
  style?: any;
}

// Destructuring Astro.props to get the component's props
let {
  src,
  alt,
  width,
  height,
  loading,
  decoding,
  class: className,
  format,
  style,
} = Astro.props;

src = `/public${src}`;

// Glob pattern to load images from the /public/images folder
const images = import.meta.glob("/public/images/**/*.{jpeg,jpg,png,gif}");

// Check if the source path is valid
const isValidPath = images[src] ? true : false;

// Log a warning message in red if the image is not found
!isValidPath &&
  console.error(
    `\x1b[31mImage not found - ${src}.\x1b[0m Make sure the image is in the /public/images folder.`,
  );
---

{
  isValidPath && (
    <Image
      src={images[src]() as Promise<{ default: ImageMetadata }>}
      alt={alt}
      width={width}
      height={height}
      loading={loading}
      decoding={decoding}
      class={className}
      format={format}
      style={style}
    />
  )
}
