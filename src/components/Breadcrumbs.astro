---
import { humanize } from "@/lib/utils/textConverter";

const { className, pageType, pageTitle }: { className?: string, pageType?: string, pageTitle?: string } = Astro.props;

const paths = Astro.url.pathname.split("/").filter((x) => x);
let parts = [
  {
    label: "首页",
    href: "/",
    "aria-label": Astro.url.pathname === "/" ? "page" : undefined,
  },
];

// 如果有特定页面类型和标题，直接使用
if (pageType && pageTitle) {
  parts.push({
    label: pageType,
    href: `/${paths[0]}`,
    "aria-label": paths.length === 1 ? "page" : undefined,
  });
  
  if (paths.length > 1) {
    parts.push({
      label: pageTitle,
      href: Astro.url.pathname,
      "aria-label": "page",
    });
  }
} else {
  // 根据URL自动生成面包屑
  paths.forEach((segment: string, i: number) => {
  const href = `/${paths.slice(0, i + 1).join("/")}`;
    let label = "";

    // 为常见路径提供友好名称
    switch(segment) {
      case "works":
        label = "诗词";
        break;
      case "authors":
        label = "诗人";
        break;
      case "dynasties":
        label = "朝代";
        break;
      case "collections":
        label = "诗集";
        break;
      default:
        // 如果是最后一个段，且它是数字或拼音，那么使用提供的页面标题
        if (i === paths.length - 1 && pageTitle) {
          label = pageTitle;
        } else {
          label = segment;
        }
    }

    parts.push({
      label,
      href,
      "aria-label": Astro.url.pathname === href ? "page" : undefined,
    });
});
}
---

<nav aria-label="面包屑导航" class={`${className} mb-6 text-sm`}>
  <ol class="flex flex-wrap" role="list" itemscope itemtype="https://schema.org/BreadcrumbList">
    {
      parts.map(({ label, href, ...attrs }, index) => (
        <li class="flex items-center" role="listitem" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
          {index > 0 && <span class="mx-2 text-gray-400">/</span>}
          {index !== parts.length - 1 ? (
            <a 
              class="text-gray-600 hover:text-primary dark:text-gray-400 dark:hover:text-gray-200" 
              href={href}
              itemprop="item"
              {...attrs}
            >
              <span itemprop="name">{label}</span>
            </a>
          ) : (
            <span class="text-gray-900 font-medium dark:text-gray-200" itemprop="name">{label}</span>
          )}
          <meta itemprop="position" content={(index + 1).toString()} />
        </li>
      ))
    }
  </ol>
</nav>
