/**
 * 页面访问统计跟踪器
 * 用于记录网站不同页面的访问次数
 */

// 定义页面类型常量
export const PAGE_TYPES = {
  WORK: 'work',         // 作品页面
  AUTHOR: 'author',     // 作者页面
  DYNASTY: 'dynasty',   // 朝代页面
  COLLECTION: 'collection', // 诗集页面
  OTHER: 'other'        // 其他页面
};

/**
 * 记录页面访问
 * @param {string} path - 页面路径
 * @param {string} pageType - 页面类型，使用PAGE_TYPES常量
 * @param {number|null} entityId - 可选的实体ID
 * @returns {Promise<{success: boolean, views?: number, error?: string}>}
 */
export async function trackPageView(path, pageType, entityId = null) {
  try {
    // 防止重复统计，使用sessionStorage检查当前会话是否已记录此页面
    const sessionKey = `pageview_${path}`;
    if (typeof window !== 'undefined' && window.sessionStorage) {
      if (sessionStorage.getItem(sessionKey)) {
        return { success: true, alreadyCounted: true };
      }
    }

    const response = await fetch('/api/pageview', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        path, 
        pageType, 
        entityId
      })
    });

    const data = await response.json();
    
    // 标记当前会话已记录此页面
    if (typeof window !== 'undefined' && window.sessionStorage && data.success) {
      sessionStorage.setItem(sessionKey, 'true');
    }
    
    return data;
  } catch (error) {
    console.error('跟踪页面访问失败:', error);
    return { success: false, error: '请求失败' };
  }
} 