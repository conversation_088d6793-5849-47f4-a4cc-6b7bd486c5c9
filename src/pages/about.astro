---
import Base from "@/layouts/Base.astro";
import { markdownify } from "@/lib/utils/textConverter";
import SubTitle from "@/partials/SubTitle.astro";
import { getEntry } from "astro:content";

export const prerender = true;

const about = await getEntry("about", "-index");
const { Content } = await about.render();
const { title, description, meta_title, image } = about.data;
---

<Base
  title={title}
  meta_title={meta_title}
  description={description}
>
  <section class="section px-4 pt-0 pb-0 my-14 mx-auto">
    <div class="container">
      <div class="row justify-center mb-14">
        <div class="md:col-10 lg:col-10">
          <h3 set:html={markdownify(title)} class="text-2xl text-center mb-4" />
          <p set:html={markdownify(description)} class="text-center mb-8"/>
          <div class="content leading-loose" style="text-indent: 2em;">
            <Content />
          </div>
        </div>
      </div>
    </div>
  </section>
</Base>
