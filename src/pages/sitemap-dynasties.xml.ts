import type { APIRoute } from 'astro';
import config from "@/config/config.json";

export const GET: APIRoute = async () => {
  const baseUrl = config.site.base_url || "https://meishici.com";
  const now = new Date().toISOString();
  
  // 这里应该从数据库获取所有朝代的信息
  // 由于我们无法直接访问数据库，这里创建一个基本的结构
  // 实际部署时，应该替换为真实的数据查询
  
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <!-- 这里应该动态生成所有朝代的URL -->
  <url>
    <loc>${baseUrl}/dynasties</loc>
    <lastmod>${now}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.7</priority>
  </url>
</urlset>`;

  return new Response(sitemap, {
    headers: {
      'Content-Type': 'application/xml'
    }
  });
};