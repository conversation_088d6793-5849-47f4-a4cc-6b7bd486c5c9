---
import Base from "@/layouts/Base.astro";
import config from "@/config/config.json";
import PageHeader from "@/partials/PageHeader.astro";
import { asc, eq, lt, gte, ne, inArray } from "drizzle-orm";
import {
  table_collection_works,
  table_collections,
  table_works,
} from "@/database/schema_sqlite";
import { localdb as db } from "@/database/localdb";
import SubTitle from "@/partials/SubTitle.astro";
import { Card, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";

export const prerender = true;

export async function getStaticPaths() {
  const list = await db.select().from(table_collections);
  return list.map((item) => {
    return {
      params: { slug: item.slug || `item-${item.id}` },
    };
  });
}

const { slug } = Astro.params;

// 先根据slug查询，如果找不到则尝试使用ID（为了兼容旧链接）
let collection = (
  await db.select().from(table_collections).where(eq(table_collections.slug, slug))
)[0];

// 如果没有找到，可能是旧的数字ID链接
if (!collection && /^\d+$/.test(slug)) {
  collection = (
    await db.select().from(table_collections).where(eq(table_collections.id, parseInt(slug, 10)))
  )[0];
}

// 如果collection不存在，提供一个错误页面或重定向
if (!collection) {
  return Astro.redirect('/404');
}

const works = await db
  .select()
  .from(table_collection_works)
  .where(eq(table_collection_works.collection_id, collection.id))
  .orderBy(asc(table_collection_works.show_order));

// 获取作品的slug信息
const workIds = works.map(work => work.work_id);
const workSlugs = workIds.length > 0 
  ? await db
      .select({ id: table_works.id, slug: table_works.slug })
      .from(table_works)
      .where(inArray(table_works.id, workIds))
  : [];

// 创建作品ID到slug的映射
const workSlugMap = {};
workSlugs.forEach(work => {
  workSlugMap[work.id] = work.slug || work.id;
});

const title = collection?.name;
const meta_title = `${collection?.name} - 精选${works.length}篇古诗词 - ${config.site.title}`;

let description = `${collection?.name}包含${works.length}篇经典诗词作品`;

if (works.length > 0) {
  description += `，如《${works[0].work_title}》`;
  if (works.length > 1) {
    description += `、《${works[1].work_title}》`;
  }
  if (works.length > 2) {
    description += `、《${works[2].work_title}》`;
  }
  if (works.length > 3) {
    description += "等";
  }
}

if (collection.desc) {
  const desc = collection.desc.replace(/\r\n/g, ' ').replace(/\n/g, ' ').trim();
  if (desc.length > 0) {
    description += `。${desc.slice(0, 100)}`;
    if (desc.length > 100) {
      description += '...';
    }
  }
}

description += ` | ${config.site.title}提供${collection?.name}所有诗词原文、注释及赏析`;

// 确保描述不超过合理长度
if (description.length > 200) {
  description = description.slice(0, 197) + '...';
}

const canonical = config.site.base_url + `/collections/${slug}`;
---

<Base 
  meta_title={meta_title} 
  description={description} 
  canonical={canonical}
  pageType="collection"
  entityId={collection.id}>
  <PageHeader title={title} desc={collection.desc} pageType="诗集" />
  
  <section class="section pt-0">
    <div class="container">
      {works.length > 0 && (
        <>
          <SubTitle
            title="作品列表"
            description={`共${works.length}篇作品`}
          />
          <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 pb-10">
            {works.map((work) => (
              <a
                href={`/works/${workSlugMap[work.work_id] || work.work_id}`}
                class="text-sm cursor-pointer"
                data-astro-reload
              >
                <Card className="hover:bg-gray-100 dark:hover:bg-gray-800 transition duration-150 ease-in-out">
                  <CardHeader>
                    <CardTitle className="text-sm line-clamp-1">
                      {work.work_title}
                    </CardTitle>
                    <CardDescription className="pt-2 line-clamp-2">
                      {work.work_dynasty} · {work.work_author}
                    </CardDescription>
                  </CardHeader>
                </Card>
              </a>
            ))}
          </div>
        </>
      )}
    </div>
  </section>

  <!-- 结构化数据 -->
  <script type="application/ld+json" set:html={JSON.stringify({
    "@context": "https://schema.org",
    "@type": "Collection",
    "name": collection.name,
    "description": collection.desc || `${collection.name}集合，包含${works.length}篇古诗词`,
    "collectionSize": works.length,
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": canonical
    },
    "hasPart": works.slice(0, 10).map(work => ({
      "@type": "CreativeWork",
      "name": work.work_title,
      "author": {
        "@type": "Person",
        "name": work.work_author
      },
      "url": `${config.site.base_url}/works/${workSlugMap[work.work_id] || work.work_id}`
    }))
  })}>
  </script>
</Base>
