import type { APIRoute } from 'astro';
import { eq, and, sql } from 'drizzle-orm';
import { localdb } from '@/database/localdb';
import { table_page_views } from '@/database/schema_sqlite';

export const POST: APIRoute = async ({ request }) => {
  try {
    const body = await request.json();
    const { path, pageType, entityId } = body;
    
    if (!path || !pageType) {
      return new Response(JSON.stringify({ error: '缺少必要参数' }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    // 检查记录是否已存在
    const existingRecord = await localdb.select()
      .from(table_page_views)
      .where(
        and(
          eq(table_page_views.path, path),
          entityId ? eq(table_page_views.entity_id, entityId) : sql`1=1`
        )
      )
      .limit(1);
    
    const now = new Date().toISOString();

    if (existingRecord.length > 0) {
      // 更新现有记录
      await localdb.update(table_page_views)
        .set({ 
          views_count: existingRecord[0].views_count + 1,
          updated_at: now
        })
        .where(eq(table_page_views.id, existingRecord[0].id));
      
      return new Response(JSON.stringify({ 
        success: true, 
        views: existingRecord[0].views_count + 1 
      }), {
        headers: {
          'Content-Type': 'application/json'
        }
      });
    } else {
      // 创建新记录
      const result = await localdb.insert(table_page_views)
        .values({
          path,
          page_type: pageType,
          entity_id: entityId || null,
          views_count: 1,
          updated_at: now
        })
        .returning();
      
      return new Response(JSON.stringify({ 
        success: true, 
        views: 1
      }), {
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
  } catch (error) {
    console.error('页面统计错误:', error);
    return new Response(JSON.stringify({ error: '服务器错误' }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
}; 