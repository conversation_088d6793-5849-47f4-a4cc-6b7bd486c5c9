---
import Base from "@/layouts/Base.astro";
import { getSinglePage } from "@/lib/contentParser.astro";
import PageHeader from "@/partials/PageHeader.astro";

export const prerender = true;

// get static paths for all pages
export async function getStaticPaths() {
  const COLLECTION_FOLDER = "pages";

  const pages = await getSinglePage(COLLECTION_FOLDER);

  const paths = pages.map((page) => ({
    params: {
      regular: page.slug,
    },
    props: { page },
  }));
  return paths;
}

const { page } = Astro.props;
const { title, meta_title, description, image } = page.data;
const { Content } = await page.render();
---

<Base
  title={title}
  meta_title={meta_title}
  description={description}
  image={image}
>
  <PageHeader title={title} />
  <section class="section-sm">
    <div class="container">
      <div class="row justify-center">
        <div class="lg:col-10">
          <div class="content">
            <Content />
          </div>
        </div>
      </div>
    </div>
  </section>
</Base>
