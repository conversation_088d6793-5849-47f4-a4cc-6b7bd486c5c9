---
import Base from "@/layouts/Base.astro";

export const prerender = true;

const dev = import.meta.env.DEV;
const prod = import.meta.env.PROD;
const mode = import.meta.env.MODE;
const baseUrl = import.meta.env.BASE_URL;

// Node环境信息
const ci = import.meta.env.CI;
const nodeEnv = process.env.NODE_ENV;
const hostname = import.meta.env.HOST || 'localhost';
const port = import.meta.env.PORT || '4321';

// 添加服务器信息
const serverInfo = {
  platform: process.platform,
  nodeVersion: process.version,
  env: nodeEnv,
  hostname: hostname,
  port: port
};
---

<Base title="INFO">
  <section class="section">
    <div class="container">
      <div>
        <h2 class="mb-4 text-xl font-semibold">环境信息</h2>
        <!-- 显示环境变量 -->
        <pre class="justify-start text-start">{JSON.stringify(import.meta.env, null, 2)}</pre>
        
        <h2 class="mb-4 mt-6 text-xl font-semibold">服务器信息</h2>
        <!-- 显示服务器信息 -->
        <pre class="justify-start text-start">{JSON.stringify(serverInfo, null, 2)}</pre>
      </div>
    </div>
  </section>
</Base>