---
import Base from "@/layouts/Base.astro";
import config from "@/config/config.json";
import { asc, eq, inArray } from "drizzle-orm";
import {
  table_collection_works,
  table_works,
  table_quotes,
  table_authors,
  table_dynasties,
  table_collections,
} from "@/database/schema_sqlite";
import { localdb as db } from "@/database/localdb";
import SubTitle from "@/partials/SubTitle.astro";
import Breadcrumbs from "@/components/Breadcrumbs.astro";

export const prerender = true;

export async function getStaticPaths() {
  const list = await db.select().from(table_works);
  return list.map((item) => {
    return {
      params: { slug: item.slug || `item-${item.id}` },
    };
  });
}

const { slug } = Astro.params;

// 先根据slug查询，如果找不到则尝试使用ID（为了兼容旧链接）
let work = (
  await db.select().from(table_works).where(eq(table_works.slug, slug))
)[0];

// 如果没有找到，可能是旧的数字ID链接
if (!work && /^\d+$/.test(slug)) {
  work = (
    await db.select().from(table_works).where(eq(table_works.id, parseInt(slug, 10)))
  )[0];
}

// 如果work不存在，提供一个错误页面或重定向
if (!work) {
  return Astro.redirect('/404');
}

const author_id = work.author_id;
const author_name = work.author;

// 查询作者的slug，用于链接
const author = (
  await db.select().from(table_authors).where(eq(table_authors.id, author_id))
)[0];
const author_slug = author?.slug || author_id;

// 查询朝代的slug，用于链接
const dynasty_name = work.dynasty;
const dynasty = (
  await db.select().from(table_dynasties).where(eq(table_dynasties.name, dynasty_name))
)[0];
const dynasty_slug = dynasty?.slug || dynasty_name;

const collections = await db
  .select()
  .from(table_collection_works)
  .where(eq(table_collection_works.work_id, work.id))
  .orderBy(asc(table_collection_works.show_order));

// 获取集合的slug信息
const collection_ids = collections.map(c => c.collection_id);
const collection_slugs = collection_ids.length > 0 
  ? await db
      .select()
      .from(table_collections)
      .where(inArray(table_collections.id, collection_ids))
  : [];

// 创建集合ID到slug的映射
const collectionSlugMap = {};
collection_slugs.forEach(c => {
  collectionSlugMap[c.id] = c.slug || c.id;
});

const quotes = await db
  .select()
  .from(table_quotes)
  .where(eq(table_quotes.work_id, work.id));

const title = work.title;
const meta_title = `${work.title} - ${work.dynasty}${author_name ? ' · '+author_name : ''} - 古诗词 - ${config.site.title}`;

// 创建更有描述性的description，包含朝代、作者和诗词的前150个字符
let description = `${work.dynasty}${author_name ? '·'+author_name : ''}的《${work.title}》`;
if (work.content) {
  const content = work.content.replace(/\n/g, ' ').trim();
  description += `：${content.slice(0, 100)}...`;
}
if (description.length > 160) {
  description = description.slice(0, 157) + '...';
}

description += ` | ${config.site.title}提供《${work.title}》原文、注释、翻译及赏析`;

const canonical = config.site.base_url + `/works/${slug}`;
---

<Base 
  meta_title={meta_title} 
  description={description} 
  canonical={canonical}
  pageType="work"
  entityId={work.id}>
  <section class="section px-4 pt-0 pb-0 mb-14 mx-auto">
    <div class="container">
      <!-- 面包屑导航放在容器顶部 -->
      <div class="mb-6 mt-4">
        <Breadcrumbs pageType="诗词" pageTitle={work.title} />
      </div>
    
      <div class="flex flex-col md:flex-row md:gap-8">
        <!-- 左侧区域 -->
        <div class="md:w-2/3">
          <!-- 作品内容 - 放在长方形样式框中 -->
          <div class="border rounded-lg p-6 mb-8 bg-white dark:bg-gray-800 shadow">
            <h1 class="text-2xl text-center">{work.title}</h1>

            <div class="flex justify-center items-center mb-6 mt-4">
              <a
                href={`/dynasties/${dynasty_slug}`}
                class="animated-underline p-1"
                >{work.dynasty}</a
              >
              <span class="mx-1 text-gray-400">·</span>
              <a
                href={`/authors/${author_slug}`}
                class="animated-underline p-1"
                >{author_name}</a
              >
            </div>

            <!-- 根据布局类型渲染内容 -->
            {work.layout === "center" || !work.layout ? (
              // 居中布局 - 分行显示
              <div class="flex justify-center">
                <pre
                  class="whitespace-pre-wrap leading-loose poetry-content
                  text-dark dark:text-darkmode-dark"
                >{work.content}
                </pre>
              </div>
            ) : work.layout === "indent" ? (
              // 段落布局 - 使用缩进
              <div class="flex justify-center">
                <pre
                  class="whitespace-pre-wrap leading-loose poetry-content
                      text-dark dark:text-darkmode-dark"
                >{work.content}
                </pre>
              </div>
            ) : (
              // 默认布局
              <div class="flex justify-center">
                <pre
                  class="whitespace-pre-wrap leading-loose poetry-content
                      text-dark dark:text-darkmode-dark"
                >{work.content}
                </pre>
              </div>
            )}
          </div>

          <!-- 左侧下方模块，按顺序排列 -->
          
          <!-- 作品简介 -->
          {work.intro && (
            <div class="mb-6">
              <SubTitle title="简介" description="" />
              <div class="flex justify-start">
                <pre class="whitespace-pre-wrap">{work.intro}</pre>
              </div>
            </div>
          )}
          
          <!-- 作品注解 -->
          {(work.notes || work.annotation) && (
            <div class="mb-6">
              <SubTitle title="注解" description="" />
              <div class="flex justify-start">
                <pre class="whitespace-pre-wrap">{work.notes || work.annotation}</pre>
              </div>
            </div>
          )}
          
          <!-- 作品翻译 -->
          {(work.trans || work.translation) && (
            <div class="mb-6">
              <SubTitle title="翻译" description="" />
              <div class="flex justify-start">
                <pre class="whitespace-pre-wrap">{work.trans || work.translation}</pre>
              </div>
            </div>
          )}
          
          <!-- 作品评价 -->
          {work.master_comment && (
            <div class="mb-6">
              <SubTitle title="评价" description="" />
              <div class="flex justify-start">
                <pre class="whitespace-pre-wrap">{work.master_comment}</pre>
              </div>
            </div>
          )}
          
          <!-- 诗集列表 - 横向排列 -->
          {collections.length > 0 && (
            <div class="mb-6">
              <SubTitle title="诗集" description="" />
              <div class="flex flex-wrap gap-y-1 gap-x-2">
                {collections.map((collection) => {
                  const collectionSlug = collectionSlugMap[collection.collection_id] || collection.collection_id;
                  return (
                    <a
                      href={`/collections/${collectionSlug}`}
                      class="animated-underline p-1"
                    >
                      # {collection.collection_name || collection.collection}
                    </a>
                  )
                })}
              </div>
            </div>
          )}
        </div>

        <!-- 右侧区域 - 佳句 -->
        <div class="md:w-1/3 mt-8 md:mt-0">
          {quotes.length > 0 && (
            <div>
              <SubTitle title="佳句" description="" />
              <div class="border rounded-lg p-4 bg-white dark:bg-gray-800">
                <ul class="space-y-3">
                  {quotes.map((quote) => (
                    <li class="p-2 border-b border-gray-100 dark:border-gray-700 last:border-0">{quote.content || quote.quote}</li>
                  ))}
                </ul>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  </section>

  <!-- 保留结构化数据 -->
  <script type="application/ld+json" set:html={JSON.stringify({
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": work.title,
    "datePublished": new Date().toISOString(),
    "author": {
      "@type": "Person",
      "name": author_name || "佚名"
    },
    "publisher": {
      "@type": "Organization",
      "name": config.site.title,
      "logo": {
        "@type": "ImageObject",
        "url": `${config.site.base_url}${config.site.favicon}`
      }
    },
    "description": description,
    "isPartOf": {
      "@type": "CreativeWork",
      "name": work.dynasty,
      "description": `${work.dynasty}诗词作品集`
    },
    "articleBody": work.content
  })}>
  </script>
</Base>
