---
import Base from "@/layouts/Base.astro";
import config from "@/config/config.json";
import { asc, eq } from "drizzle-orm";
import {
  table_collection_works,
  table_works,
  table_quotes,
} from "@/database/schema_sqlite";
import { localdb as db } from "@/database/localdb";
import SubTitle from "@/partials/SubTitle.astro";

export const prerender = true;

export async function getStaticPaths() {
  const list = await db.select().from(table_works);
  return list.map((item) => {
    return {
      params: { id: item.id },
    };
  });
}

const { id } = Astro.params;

const work = (
  await db.select().from(table_works).where(eq(table_works.id, id))
)[0];
// console.log(work);

// 如果有slug可用，重定向到slug URL以确保规范URL
if (work && work.slug) {
  return Astro.redirect(`/works/${work.slug}`, 301);
}

const author_id = work.author_id;
const author_name = work.author;

const collections = await db
  .select()
  .from(table_collection_works)
  .where(eq(table_collection_works.work_id, id))
  .orderBy(asc(table_collection_works.show_order));

const quotes = await db
  .select()
  .from(table_quotes)
  .where(eq(table_quotes.work_id, id));

const title = work.title;
const meta_title = `${work.title} - ${work.dynasty}${author_name ? ' - '+author_name : ''} - ${config.site.title}`;

// 创建更有描述性的description，包含朝代、作者和诗词的前150个字符
let description = `${work.dynasty}${author_name ? '·'+author_name : ''}的《${work.title}》`;
if (work.content) {
  const content = work.content.replace(/\n/g, ' ').trim();
  description += `：${content.slice(0, 100)}...`;
}
if (description.length > 160) {
  description = description.slice(0, 157) + '...';
}

const canonicalSlug = work.slug || id;
const canonical = config.site.base_url + `/works/${canonicalSlug}`;
// console.log('astro canonical: ', Astro.url);
---

<Base meta_title={meta_title} description={description} canonical={canonical}>
  <section class="section px-4 pt-0 pb-0 mb-14 mx-auto">
    <div class="container">
      <div class="flex flex-col md:flex-row md:gap-8 mt-14">
        <!-- 左侧区域 -->
        <div class="md:w-2/3">
          <!-- 作品内容 - 放在长方形样式框中 -->
          <div class="border rounded-lg p-6 mb-8 bg-white dark:bg-gray-800 shadow">
          <h1 class="text-2xl text-center">{work.title}</h1>

            <div class="flex justify-center items-center mb-6 mt-4">
            <a
              href={`/dynasties/${work.dynasty}`}
                class="animated-underline p-1"
                >{work.dynasty}</a
            >
              <span class="mx-1 text-gray-400">·</span>
            <a
              href={`/authors/${author_id}`}
                class="animated-underline p-1"
              >{author_name}</a
            >
          </div>

          <!-- 居中布局 -->
          {
            work.layout == "center" && (
              <div class="flex justify-center">
                <pre
                  class="whitespace-pre-wrap leading-loose poetry-content
                  text-dark dark:text-darkmode-dark"
                >{work.content}
                </pre>
              </div>
            )
          }

          <!-- 段落布局，暂时和居中布局效果一样 -->
          {
            work.layout == "indent" && (
              <div class="flex justify-center">
                <pre
                  class="whitespace-pre-wrap leading-loose poetry-content
                      text-dark dark:text-darkmode-dark"
                >{work.content}
                </pre>
              </div>
            )
          }
      </div>

          <!-- 左侧下方模块，按顺序排列 -->
          
          <!-- 作品简介 -->
          {
            work.intro && (
              <div class="mb-6">
                <SubTitle title="简介" description="" />
                <div class="flex justify-start">
                  <pre class="whitespace-pre-wrap">{work.intro}</pre>
                </div>
              </div>
            )
          }
          
          <!-- 作品注解 -->
          {
            work.annotation && (
              <div class="mb-6">
                <SubTitle title="注解" description="" />
                <div class="flex justify-start">
                  <pre class="whitespace-pre-wrap">{work.annotation}</pre>
                </div>
              </div>
            )
          }
          
          <!-- 作品翻译 -->
          {
            work.translation && (
              <div class="mb-6">
                <SubTitle title="翻译" description="" />
                <div class="flex justify-start">
                  <pre class="whitespace-pre-wrap">{work.translation}</pre>
                </div>
              </div>
            )
          }
          
          <!-- 作品评价 -->
          {
            work.master_comment && (
              <div class="mb-6">
                <SubTitle title="评价" description="" />
                <div class="flex justify-start">
                  <pre class="whitespace-pre-wrap">{work.master_comment}</pre>
                </div>
              </div>
            )
          }
          
          <!-- 诗集列表 -->
          {
            collections.length > 0 && (
              <div class="mb-6">
                <SubTitle title="诗集" description="" />
                <div class="flex flex-wrap gap-y-1 gap-x-2">
                  {collections.map((collection) => (
                    <a
                      href={`/collections/${collection.collection_id}`}
                      class="animated-underline p-1"
                    >
                      # {collection.collection}
                    </a>
                  ))}
                </div>
              </div>
            )
          }
        </div>

        <!-- 右侧区域 - 佳句 -->
        <div class="md:w-1/3 mt-8 md:mt-0">
          {
            quotes.length > 0 && (
              <div>
                <SubTitle title="佳句" description="" />
                <div class="border rounded-lg p-4 bg-white dark:bg-gray-800">
                  <ul class="space-y-3">
                    {quotes.map((quote) => (
                      <li class="p-2 border-b border-gray-100 dark:border-gray-700 last:border-0">{quote.quote}</li>
                    ))}
                  </ul>
                </div>
              </div>
            )
          }
        </div>
      </div>
    </div>
  </section>
</Base>
