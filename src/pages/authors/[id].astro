---
import Base from "@/layouts/Base.astro";
import config from "@/config/config.json";
import PageHeader from "@/partials/PageHeader.astro";
import { desc, eq, lt, gte, ne } from "drizzle-orm";
import { table_authors, table_works } from "@/database/schema_sqlite";
import { localdb as db } from "@/database/localdb";
import SubTitle from "@/partials/SubTitle.astro";
import SimpleSpan from "@/components/SimpleSpan.astro";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";

export const prerender = true;

export async function getStaticPaths() {
  const list = await db.select().from(table_authors);
  return list.map((item) => {
    return {
      params: { id: item.id },
    };
  });
}

const { id } = Astro.params;

const author = (
  await db.select().from(table_authors).where(eq(table_authors.id, id))
)[0];

// 如果作者存在且有slug，重定向到slug URL以确保规范URL
if (author && author.slug) {
  return Astro.redirect(`/authors/${author.slug}`, 301);
}

const works = await db
  .select()
  .from(table_works)
  .where(eq(table_works.author_id, id))
  .orderBy(desc(table_works.show_order));

const title = author.name;
const meta_title = `${author.name} - ${author.dynasty}诗人 - ${config.site.title}`;

let description = `${author.dynasty}诗人${author.name}`;
if (works.length > 0) {
  description += `的代表作品有《${works[0].title}》`;
  if (works.length > 1) {
    description += `、《${works[1].title}》`;
  }
  if (works.length > 2) {
    description += `、《${works[2].title}》`;
  }
  description += "等";
}
if (author.intro) {
  const intro = author.intro.replace(/\r\n/g, ' ').replace(/\n/g, ' ').trim();
  const remainingLength = 160 - description.length;
  if (remainingLength > 20 && intro.length > 0) {
    description += `。${intro.slice(0, remainingLength)}`;
    if (description.length > 157 && intro.length > remainingLength) {
      description = description.slice(0, 157) + '...';
    }
  }
}

const canonical = config.site.base_url + `/authors/${id}`;
---

<Base meta_title={meta_title} description={description} canonical={canonical}>
  <PageHeader title={title} />
  <section class="section px-4 pt-0 pb-0 mb-14 mx-auto">
    <div class="container">
      <!-- 作者简介 -->
      <SubTitle title="简介" description="" />
      <SimpleSpan content={author.intro} />

      <!-- 作品列表 -->
      <SubTitle title="作品" description={`(${works.length})`} />
      <div
        class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
      >
        {
          works.map((work, index) => (
            <div>
              <a data-astro-reload
                href={`/works/${work.slug || work.id}`}
                title={work.title}
                class="text-sm cursor-pointer"
              >
                <Card className="hover:bg-gray-100 dark:hover:bg-gray-800 transition duration-150 ease-in-out">
                  <CardHeader>
                    <CardTitle className="text-sm line-clamp-1">
                      {work.title}
                      {work.quotes_count >= 2 && ` 🔥`}
                    </CardTitle>
                    <CardDescription className="pt-2 text-sm line-clamp-1">
                      {work.dynasty} - {work.author}
                    </CardDescription>
                  </CardHeader>
                </Card>
              </a>
            </div>
          ))
        }
      </div>
    </div>
  </section>
</Base>
