---
import Base from "@/layouts/Base.astro";
import config from "@/config/config.json";
import PageHeader from "@/partials/PageHeader.astro";
import { desc, eq, lt, gte, ne } from "drizzle-orm";
import { table_authors, table_works } from "@/database/schema_sqlite";
import { localdb as db } from "@/database/localdb";
import SubTitle from "@/partials/SubTitle.astro";
import SimpleSpan from "@/components/SimpleSpan.astro";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";

export const prerender = true;

export async function getStaticPaths() {
  const list = await db.select().from(table_authors);
  return list.map((item) => {
    return {
      params: { slug: item.slug || `item-${item.id}` },
    };
  });
}

const { slug } = Astro.params;

// 先根据slug查询，如果找不到则尝试使用ID（为了兼容旧链接）
let author = (
  await db.select().from(table_authors).where(eq(table_authors.slug, slug))
)[0];

// 如果没有找到，可能是旧的数字ID链接
if (!author && /^\d+$/.test(slug)) {
  author = (
    await db.select().from(table_authors).where(eq(table_authors.id, parseInt(slug, 10)))
  )[0];
}

// 如果author不存在，提供一个错误页面或重定向
if (!author) {
  return Astro.redirect('/404');
}

const works = await db
  .select()
  .from(table_works)
  .where(eq(table_works.author_id, author.id))
  .orderBy(desc(table_works.show_order));

const title = author.name;
const meta_title = `${author.name} - ${author.dynasty}诗人 - 生平简介 - ${config.site.title}`;

let description = `${author.dynasty}诗人${author.name}`;
if (works.length > 0) {
  description += `的代表作品有《${works[0].title}》`;
  if (works.length > 1) {
    description += `、《${works[1].title}》`;
  }
  if (works.length > 2) {
    description += `、《${works[2].title}》`;
  }
  description += "等";
}

// 添加作者简介（如果有）
if (author.intro && author.intro.length > 0) {
  const introText = author.intro.replace(/\n/g, ' ').trim();
  description += `。${introText.slice(0, 100)}`;
  if (introText.length > 100) {
    description += '...';
  }
}

description += ` | ${config.site.title}提供${author.name}诗词全集及生平介绍`;

// 确保描述不超过合理长度
if (description.length > 200) {
  description = description.slice(0, 197) + '...';
}

const canonical = config.site.base_url + `/authors/${slug}`;
---

<Base 
  meta_title={meta_title} 
  description={description} 
  canonical={canonical}
  pageType="author"
  entityId={author.id}>
  <PageHeader title={title} desc={author.intro} pageType="诗人" />

  <section class="section pt-0">
    <div class="container">
      {works.length > 0 && (
        <div class="pb-10">
          <SubTitle
            title="作品列表"
            description={`共${works.length}篇作品`}
          />
          <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {works.map((work) => (
              <a
                href={`/works/${work.slug || work.id}`}
                class="text-sm cursor-pointer"
                data-astro-reload
              >
                <Card className="hover:bg-gray-100 dark:hover:bg-gray-800 transition duration-150 ease-in-out">
                  <CardHeader>
                    <CardTitle className="text-sm line-clamp-1">
                      {work.title}
                    </CardTitle>
                    <CardDescription className="pt-2 line-clamp-2">
                      {work.content.split('\n')[0]}
                    </CardDescription>
                  </CardHeader>
                </Card>
              </a>
            ))}
          </div>
        </div>
      )}
    </div>
  </section>

  <!-- 结构化数据 -->
  <script type="application/ld+json" set:html={JSON.stringify({
    "@context": "https://schema.org",
    "@type": "Person",
    "name": author.name,
    "description": author.intro || `${author.dynasty}诗人${author.name}`,
    "knowsAbout": [
      "古典文学",
      "古典诗词",
      author.dynasty + "文学"
    ],
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": canonical
    },
    "works": works.slice(0, 10).map(work => ({
      "@type": "CreativeWork",
      "name": work.title,
      "url": `${config.site.base_url}/works/${work.slug || work.id}`
    }))
  })}>
  </script>
</Base>
