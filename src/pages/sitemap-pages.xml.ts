import type { APIRoute } from 'astro';
import config from "@/config/config.json";

export const GET: APIRoute = async () => {
  const baseUrl = config.site.base_url || "https://meishici.com";
  const now = new Date().toISOString();
  
  // 基本页面列表
  const pages = [
    "",                 // 首页
    "about",           // 关于页面
    "authors",         // 诗人列表
    "works",           // 作品列表
    "dynasties",       // 朝代列表
    "collections",     // 诗集列表
    "today",           // 今日推荐
    "hot-works"        // 热门作品
  ];
  
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  ${pages.map(page => `
  <url>
    <loc>${baseUrl}/${page}</loc>
    <lastmod>${now}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>${page === "" ? "1.0" : "0.8"}</priority>
  </url>`).join('')}
</urlset>`;

  return new Response(sitemap, {
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=3600'
    }
  });
}; 