---
import Base from "@/layouts/Base.astro";
import config from "@/config/config.json";
import PageHeader from "@/partials/PageHeader.astro";
import { localdb as db } from "@/database/localdb";
import {
  table_dynasties,
  table_works,
  table_authors,
} from "@/database/schema_sqlite";
import { desc, eq, lt, gte, ne, inArray } from "drizzle-orm";
import { Button } from "@/components/ui/button";
import SubTitle from "@/partials/SubTitle.astro";
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export const prerender = true;

export async function getStaticPaths() {
  const list = await db.select().from(table_dynasties);
  return list.map((item) => {
    return {
      params: { slug: item.slug || `item-${item.id}` },
    };
  });
}

const { slug } = Astro.params;

// 先根据slug查询，如果找不到则尝试使用name（为了兼容旧链接）
let dynasty = (
  await db.select().from(table_dynasties).where(eq(table_dynasties.slug, slug))
)[0];

// 如果没有找到，可能是旧的朝代名称链接
if (!dynasty) {
  dynasty = (
    await db.select().from(table_dynasties).where(eq(table_dynasties.name, slug))
  )[0];
}

// 如果dynasty不存在，提供一个错误页面或重定向
if (!dynasty) {
  return Astro.redirect('/404');
}

const authors = await db
  .select()
  .from(table_authors)
  .where(eq(table_authors.dynasty, dynasty.name))
  .orderBy(desc(table_authors.views_count));

// 获取作者的slug信息
const authorIds = authors.map(author => author.id);
const authorSlugs = authorIds.length > 0 
  ? await db
      .select({ id: table_authors.id, slug: table_authors.slug })
      .from(table_authors)
      .where(inArray(table_authors.id, authorIds))
  : [];

// 创建作者ID到slug的映射
const authorSlugMap = {};
authorSlugs.forEach(author => {
  authorSlugMap[author.id] = author.slug || author.id;
});

const works = await db
  .select()
  .from(table_works)
  .where(eq(table_works.dynasty, dynasty.name))
  .orderBy(desc(table_works.show_order));

// 获取作品的slug信息
const workIds = works.map(work => work.id);
const workSlugs = workIds.length > 0 
  ? await db
      .select({ id: table_works.id, slug: table_works.slug })
      .from(table_works)
      .where(inArray(table_works.id, workIds))
  : [];

// 创建作品ID到slug的映射
const workSlugMap = {};
workSlugs.forEach(work => {
  workSlugMap[work.id] = work.slug || work.id;
});

// const description = `诗人:${authors.length} 作品: ${works.length}`;
const title = dynasty.name;
const meta_title = `${dynasty.name}朝代 - ${authors.length}位诗人 - ${works.length}篇诗词 - ${config.site.title}`;

// 创建更有描述性的description
let description = `${dynasty.name}共有${authors.length}位诗人、${works.length}篇诗词作品`;

// 添加代表诗人信息
if (authors.length > 0) {
  description += `，代表诗人有${authors[0].name}`;
  if (authors.length > 1) {
    description += `、${authors[1].name}`;
  }
  if (authors.length > 2) {
    description += `、${authors[2].name}`;
  }
  if (authors.length > 3) {
    description += "等";
  }
}

// 添加朝代简介
if (dynasty.intro) {
  const intro = dynasty.intro.replace(/\r\n/g, ' ').replace(/\n/g, ' ').trim();
  if (intro.length > 0) { 
    description += `。${intro.slice(0, 100)}`;
    if (intro.length > 100) {
      description += '...';
    }
  }
}

description += ` | ${config.site.title}提供${dynasty.name}诗词全集、诗人列表及历史简介`;

// 确保描述不超过合理长度
if (description.length > 200) {
  description = description.slice(0, 197) + '...';
}

const canonical = config.site.base_url + `/dynasties/${slug}`;
---

<Base 
  meta_title={meta_title} 
  description={description} 
  canonical={canonical}
  pageType="dynasty"
  entityId={dynasty.id}>
  <PageHeader title={title} desc={dynasty.intro} pageType="朝代" />

  <section class="section pt-0">
    <div class="container">
      {authors.length > 0 && (
        <div class="pb-10">
          <SubTitle
            title="诗人列表"
            description={`共${authors.length}位诗人`}
          />
          <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {authors.map((author) => (
              <a
                href={`/authors/${authorSlugMap[author.id] || author.id}`}
                class="text-sm cursor-pointer"
              >
                <Card className="hover:bg-gray-100 dark:hover:bg-gray-800 transition duration-150 ease-in-out">
                  <CardHeader>
                    <CardTitle className="text-sm line-clamp-1">
                      {author.name}
                    </CardTitle>
                    <CardDescription className="pt-2 line-clamp-2">
                      {author.intro?.substring(0, 60) || "暂无简介"}
                      {author.intro?.length > 60 ? "..." : ""}
                    </CardDescription>
                  </CardHeader>
                </Card>
              </a>
            ))}
          </div>
        </div>
      )}

      {works.length > 0 && (
        <div>
          <SubTitle
            title="作品列表"
            description={`共${works.length}篇作品`}
          />
          <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {works.map((work) => (
              <a
                href={`/works/${workSlugMap[work.id] || work.id}`}
                class="text-sm cursor-pointer"
                data-astro-reload
              >
                <Card className="hover:bg-gray-100 dark:hover:bg-gray-800 transition duration-150 ease-in-out">
                  <CardHeader>
                    <CardTitle className="text-sm line-clamp-1">
                      {work.title}
                    </CardTitle>
                    <CardDescription className="pt-2 line-clamp-2">
                      {work.author}
                    </CardDescription>
                  </CardHeader>
                </Card>
              </a>
            ))}
          </div>
        </div>
      )}
    </div>
  </section>

  <!-- 结构化数据 -->
  <script type="application/ld+json" set:html={JSON.stringify({
    "@context": "https://schema.org",
    "@type": "ItemList",
    "name": `${dynasty.name}诗人和作品集`,
    "description": dynasty.intro || `${dynasty.name}朝代的诗人和作品`,
    "itemListElement": [
      ...authors.slice(0, 5).map((author, index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "item": {
          "@type": "Person",
          "name": author.name,
          "url": `${config.site.base_url}/authors/${authorSlugMap[author.id] || author.id}`
        }
      })),
      ...works.slice(0, 5).map((work, index) => ({
        "@type": "ListItem",
        "position": authors.slice(0, 5).length + index + 1,
        "item": {
          "@type": "CreativeWork",
          "name": work.title,
          "author": {
            "@type": "Person",
            "name": work.author
          },
          "url": `${config.site.base_url}/works/${workSlugMap[work.id] || work.id}`
        }
      }))
    ]
  })}>
  </script>
</Base>
