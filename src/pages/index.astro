---
import Base from "@/layouts/Base.astro";
import { localdb as db } from "@/database/localdb";
import { table_works, table_collection_kinds, table_collections } from "@/database/schema_sqlite";
import { desc, asc, eq } from "drizzle-orm";
import SubTitle from "@/partials/SubTitle.astro";
import fs from 'node:fs';
import path from 'node:path';

export const prerender = true;

// 每日一诗部分 - 开始
// 获取今天的日期并转为中文格式
const today = new Date();
const year = today.getFullYear();
const month = today.getMonth() + 1;
const day = today.getDate();
const weekDays = ['日', '一', '二', '三', '四', '五', '六'];
const weekDay = weekDays[today.getDay()];
const formattedDate = `${year}年${month}月${day}日 星期${weekDay}`;
const dateString = today.toISOString().split('T')[0];

// 读取日期映射文件获取对应的work_id
const dateConfigPath = path.join(process.cwd(), '.json', 'date.json');
const dateConfig = JSON.parse(fs.readFileSync(dateConfigPath, 'utf8'));
const todayWorkId = dateConfig[dateString] || null;

// 获取今日诗词
let todayWork = null;
if (todayWorkId) {
  const works = await db
    .select()
    .from(table_works)
    .where(eq(table_works.id, todayWorkId));
  
  if (works.length > 0) {
    todayWork = works[0];
  }
}

// 在这里添加农历和节气的计算
// 简单的农历转换（真实项目中可能需要更复杂的转换库）
// 这里只是模拟，实际项目需要使用专门的农历转换库
function getLunarDate() {
  // 简单返回固定格式，实际项目需要真实计算
  const lunarMonths = ['正', '二', '三', '四', '五', '六', '七', '八', '九', '十', '冬', '腊'];
  const lunarDays = ['初一', '初二', '初三', '初四', '初五', '初六', '初七', '初八', '初九', '初十',
    '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十',
    '廿一', '廿二', '廿三', '廿四', '廿五', '廿六', '廿七', '廿八', '廿九', '三十'];
  
  // 简化处理，实际需使用农历库
  const lunarMonth = lunarMonths[month - 1];
  const lunarDay = lunarDays[day % 30];
  
  return `${lunarMonth}月${lunarDay}`;
}

// 二十四节气判断（简化处理）
function getSolarTerm() {
  const solarTerms = [
    {date: '2024-02-04', name: '立春'},
    {date: '2024-02-19', name: '雨水'},
    {date: '2024-03-05', name: '惊蛰'},
    {date: '2024-03-20', name: '春分'},
    {date: '2024-04-04', name: '清明'},
    {date: '2024-04-20', name: '谷雨'},
    {date: '2024-05-05', name: '立夏'},
    {date: '2024-05-21', name: '小满'},
    {date: '2024-06-05', name: '芒种'},
    {date: '2024-06-21', name: '夏至'},
    {date: '2024-07-07', name: '小暑'},
    {date: '2024-07-22', name: '大暑'},
    {date: '2024-08-07', name: '立秋'},
    {date: '2024-08-23', name: '处暑'},
    {date: '2024-09-07', name: '白露'},
    {date: '2024-09-22', name: '秋分'},
    {date: '2024-10-08', name: '寒露'},
    {date: '2024-10-23', name: '霜降'},
    {date: '2024-11-07', name: '立冬'},
    {date: '2024-11-22', name: '小雪'},
    {date: '2024-12-07', name: '大雪'},
    {date: '2024-12-21', name: '冬至'},
    {date: '2025-01-05', name: '小寒'},
    {date: '2025-01-20', name: '大寒'},
  ];
  
  const term = solarTerms.find(term => term.date === dateString);
  return term ? term.name : '';
}

const lunarDate = getLunarDate();
const solarTerm = getSolarTerm();
// 每日一诗部分 - 结束

// 获取热门诗词（根据show_order排序，展示前25条）
const hotWorks = await db
  .select()
  .from(table_works)
  .orderBy(desc(table_works.show_order))
  .limit(25);

// 获取诗集分类
const collectionKinds = await db
  .select()
  .from(table_collection_kinds)
  .orderBy(asc(table_collection_kinds.show_order));

// 获取诗集
const collections = await db
  .select()
  .from(table_collections)
  .where(eq(table_collections.online_data, 0));

// 将诗集按分类组织
let collectionsByKind = collectionKinds.map((collectionKind) => {
  const kindCollections = collections
    .filter((collection) => collection.kind_id === collectionKind.id)
    .slice(0, 6); // 每个分类最多显示6个标签
  
  return {
    kind: collectionKind,
    items: kindCollections.map(collection => ({
      ...collection,
      id: collection.id,
      href: `/collections/${collection.slug || collection.id}`,
      name: collection.quotes_count >= 30 ? `${collection.name} 🔥` : collection.name,
    })),
    hasMore: collections.filter((collection) => collection.kind_id === collectionKind.id).length > 6
  };
});

// 过滤掉没有内容的分类
collectionsByKind = collectionsByKind.filter(
  (collectionByKind) => collectionByKind.items.length > 0
);

// 为了SEO，设置页面标题和描述
const title = "美诗词·中国古代诗词之美";
const meta_title = "美诗词·中国古代诗词之美";
const description =
  "美诗词，领略中国古代诗词之美。" +
  "美诗词按诗集、朝代、诗人、诗词等方式检索，内容丰富，信息齐全。" +
  "美诗词按选集、主题、节日、节气、词牌、时令、地理等方式精选分类。" +
  "美诗词全站响应式布局，兼容移动端，支持暗黑模式，响应速度快。";
// console.log('index meta:', title, meta_title, description);
---

<Base title={title} meta_title={meta_title} description={description}>
  <!-- 主要内容区 -->
  <section class="section py-10">
    <div class="container">
      <div class="flex flex-col lg:flex-row gap-8">
        <!-- 左侧：每日一诗和热门诗词列表 -->
        <div class="w-full lg:w-2/3">
          <!-- 每日一诗模块 -->
          {todayWork && (
            <div class="mb-8">
              <div class="flex items-center justify-between mb-3">
                <div class="flex items-center">
                  <span class="w-1 h-5 bg-red-500 inline-block rounded mr-2"></span>
                  <h3 class="text-lg font-medium">每日一诗</h3>
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400">
                  {formattedDate}
                  <span class="ml-2">农历{lunarDate}</span>
                  {solarTerm && <span class="ml-1 text-primary">| {solarTerm}</span>}
                </div>
              </div>
              
              <div class="bg-gray-50 dark:bg-darkmode-theme-dark border border-gray-100 dark:border-gray-700 rounded-lg p-4">
                <div class="mb-2">
                  <h3 class="text-xl font-medium mb-1">
                    <a href={`/works/${todayWork.slug || todayWork.id}`} class="hover:text-primary" data-astro-reload>
                      {todayWork.title}
                    </a>
                  </h3>
                  <div class="text-sm text-gray-500 dark:text-gray-400">
                    {todayWork.dynasty} · {todayWork.author}
                  </div>
                </div>
                
                <div class="prose dark:prose-invert prose-sm max-w-none mb-2">
                  {todayWork.content.split('\n').map((line) => (
                    <p class="mb-1">{line}</p>
                  ))}
                </div>
                
                <div class="text-right">
                  <a 
                    href={`/works/${todayWork.slug || todayWork.id}`} 
                    class="text-primary text-sm hover:underline"
                    data-astro-reload
                  >
                    查看全文 →
                  </a>
                </div>
              </div>
            </div>
          )}
          
          <!-- 热门诗词列表 -->
          <div>
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center">
                <span class="w-1 h-5 bg-red-500 inline-block rounded mr-2"></span>
                <h3 class="text-lg font-medium">热门诗词</h3>
              </div>
              <a href="/hot-works/2" class="text-primary text-sm hover:underline">更多 →</a>
    </div>
            
            <div class="space-y-4">
              {hotWorks.map((work, index) => (
                <div class="bg-gray-50 dark:bg-darkmode-theme-dark border border-gray-100 dark:border-gray-700 rounded-lg p-4">
                  <h3 class="text-lg font-medium mb-1">
                    <a href={`/works/${work.slug || work.id}`} class="hover:text-primary" data-astro-reload>
                      {work.title}
                    </a>
                  </h3>
                  <div class="text-sm text-gray-500 dark:text-gray-400 mb-2">
                    {work.dynasty} · {work.author}
                  </div>
                  <div class="prose dark:prose-invert prose-sm line-clamp-3">
                    {(work.content || "").split('\n').slice(0, 3).join('\n')}
                  </div>
                  <div class="text-right mt-1">
                    <a 
                      href={`/works/${work.slug || work.id}`} 
                      class="text-primary text-sm inline-block hover:underline"
                      data-astro-reload
                    >
                      查看全文 →
                    </a>
                  </div>
                </div>
              ))}
    </div>
            
            <div class="mt-8 text-center">
              <a href="/hot-works/2" class="px-6 py-2 border border-gray-300 text-gray-600 rounded-lg hover:bg-gray-100 transition-colors dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800">
                查看更多诗词
              </a>
            </div>
          </div>
    </div>
        
        <!-- 右侧：诗集分类列表 -->
        <div class="w-full lg:w-1/3">
          <div class="bg-white dark:bg-darkmode-theme-light rounded-lg shadow p-5">
            <SubTitle title="诗集分类" description="" />
            <div class="space-y-6">
              {collectionsByKind.map((categoryItem) => (
                <div>
                  <div class="flex items-center justify-between mt-4 mb-3">
                    <div class="flex items-center">
                      <span class="bg-red-500 w-1 h-4 inline-block rounded mr-2"></span>
                      <h3 class="text-lg">{categoryItem.kind.name}</h3>
                    </div>
                    {categoryItem.hasMore && (
                      <a 
                        href={`/collections`} 
                        class="text-primary text-xs hover:underline"
                      >
                        更多 →
                      </a>
                    )}
                  </div>
                  <div class="grid grid-cols-2 gap-2">
                    {categoryItem.items.map((collection) => (
                      <a 
                        href={collection.href} 
                        class="text-sm hover:text-primary transition-colors duration-200 truncate"
                      >
                        {collection.name}
                      </a>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</Base>
