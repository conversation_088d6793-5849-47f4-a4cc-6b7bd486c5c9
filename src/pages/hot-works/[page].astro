---
import Base from "@/layouts/Base.astro";
import { localdb as db } from "@/database/localdb";
import { table_works, table_collection_kinds, table_collections } from "@/database/schema_sqlite";
import { desc, asc, eq } from "drizzle-orm";
import SubTitle from "@/partials/SubTitle.astro";
import Pagination from "@/partials/Pagination.astro";
import config from "@/config/config.json";

export const prerender = true;

// 每页显示的诗词数量
const ITEMS_PER_PAGE = 25;

// 获取页码参数
const { page } = Astro.params;
const currentPage = parseInt(page || "1");
const itemsPerPage = ITEMS_PER_PAGE;

// 获取热门诗词总数量
const rawWorks = await db
  .select()
  .from(table_works)
  .orderBy(desc(table_works.show_order));

// 确保所有必要字段都存在，防止undefined错误
const totalWorks = rawWorks.map(work => ({
  ...work,
  content: work.content || "",  // 确保content字段不为undefined
  title: work.title || "无标题", // 确保title字段不为undefined
  dynasty: work.dynasty || "",  // 确保dynasty字段不为undefined
  author: work.author || "",    // 确保author字段不为undefined
  id: work.id || 0,             // 确保id字段不为undefined
}));

const totalItems = totalWorks.length;
const totalPages = Math.ceil(totalItems / itemsPerPage);

// 页码超出范围处理
if (currentPage < 1 || currentPage > totalPages) {
  return Astro.redirect('/hot-works');
}

// 获取当前页的热门诗词
const startIndex = (currentPage - 1) * itemsPerPage;
const hotWorks = totalWorks.slice(startIndex, startIndex + itemsPerPage);

// 获取诗集分类数据（与首页相同）
const collectionKinds = await db
  .select()
  .from(table_collection_kinds)
  .orderBy(asc(table_collection_kinds.show_order));

// 获取诗集
const collections = await db
  .select()
  .from(table_collections)
  .where(eq(table_collections.online_data, 0));

// 将诗集按分类组织
let collectionsByKind = collectionKinds.map((collectionKind) => {
  const kindCollections = collections
    .filter((collection) => collection.kind_id === collectionKind.id)
    .slice(0, 6); // 每个分类最多显示6个标签
  
  return {
    kind: collectionKind,
    items: kindCollections.map(collection => ({
      ...collection,
      id: collection.id,
      href: `/collections/${collection.slug || collection.id}`,
      name: collection.quotes_count >= 30 ? `${collection.name} 🔥` : collection.name,
    })),
    hasMore: collections.filter((collection) => collection.kind_id === collectionKind.id).length > 6
  };
});

// 过滤掉没有内容的分类
collectionsByKind = collectionsByKind.filter(
  (collectionByKind) => collectionByKind.items.length > 0
);

const title = `热门诗词 - 第${currentPage}页 - ${config.site.title}`;
const description = `浏览美诗词热门诗词，第${currentPage}页，共${totalPages}页`;
const canonical = config.site.base_url + `/hot-works/${currentPage}`;

// 静态生成的页面路径
export async function getStaticPaths() {
  // 直接在函数内部定义常量，而不是引用外部常量
  const itemsPerPage = 25; 
  
  const works = await db
    .select()
    .from(table_works)
    .orderBy(desc(table_works.show_order));
    
  const totalPages = Math.ceil(works.length / itemsPerPage);
  
  return Array.from({ length: totalPages }, (_, i) => ({
    params: { page: (i + 1).toString() },
  }));
}
---

<Base title={title} description={description} canonical={canonical}>
  <section class="section px-4 pt-10 pb-10">
    <div class="container">
      <div class="flex flex-col lg:flex-row gap-8">
        <!-- 左侧：热门诗词列表 -->
        <div class="w-full lg:w-2/3">
          <div class="p-2">
            <SubTitle title="热门诗词" description={`第${currentPage}页`} />
            
            <div class="space-y-6">
              {hotWorks.map((work, index) => (
                <div class="bg-gray-50 dark:bg-darkmode-theme-dark border border-gray-100 dark:border-gray-700 rounded-lg p-4">
                  <h3 class="text-lg font-medium mb-1">
                    <a href={`/works/${work.slug || work.id}`} class="hover:text-primary" data-astro-reload>
                      {work.title}
                    </a>
                  </h3>
                  <div class="text-sm text-gray-500 dark:text-gray-400 mb-2">
                    {work.dynasty} · {work.author}
                  </div>
                  <div class="prose dark:prose-invert prose-sm line-clamp-3">
                    {(work.content || "").split('\n').slice(0, 3).join('\n')}
                  </div>
                  <div class="text-right mt-1">
                    <a 
                      href={`/works/${work.slug || work.id}`} 
                      class="text-primary text-sm inline-block hover:underline"
                      data-astro-reload
                    >
                      查看全文 →
                    </a>
                  </div>
                </div>
              ))}
            </div>
            
            <!-- 分页 -->
            <div class="mt-10">
              <Pagination 
                section="hot-works" 
                currentPage={currentPage} 
                totalPages={totalPages} 
              />
            </div>
          </div>
        </div>
        
        <!-- 右侧：诗集分类列表 -->
        <div class="w-full lg:w-1/3">
          <div class="bg-white dark:bg-darkmode-theme-light rounded-lg shadow p-5 sticky top-24">
            <SubTitle title="诗集分类" description="" />
            <div class="space-y-6">
              {collectionsByKind.map((categoryItem) => (
                <div>
                  <div class="flex items-center justify-between mt-4 mb-3">
                    <div class="flex items-center">
                      <span class="bg-red-500 w-1 h-4 inline-block rounded mr-2"></span>
                      <h3 class="text-lg">{categoryItem.kind.name}</h3>
                    </div>
                    {categoryItem.hasMore && (
                      <a 
                        href={`/collections`} 
                        class="text-primary text-xs hover:underline"
                      >
                        更多 →
                      </a>
                    )}
                  </div>
                  <div class="grid grid-cols-2 gap-2">
                    {categoryItem.items.map((collection) => (
                      <a 
                        href={collection.href} 
                        class="text-sm hover:text-primary transition-colors duration-200 truncate"
                      >
                        {collection.name}
                      </a>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</Base> 