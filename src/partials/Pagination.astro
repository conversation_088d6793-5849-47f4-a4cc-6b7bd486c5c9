---
const { section, currentPage, totalPages } = Astro.props;

// 生成一个前后各显示2个页码的分页工具
function generatePagination(current: number, total: number) {
  const pages = [];
  const delta = 2; // 当前页前后显示的页数
  
  // 首页始终显示
  pages.push(1);
  
  // 当前页前面的页数
  for (let i = Math.max(2, current - delta); i < current; i++) {
    pages.push(i);
  }
  
  // 当前页
  if (current > 1 && current < total) {
    pages.push(current);
  }
  
  // 当前页后面的页数
  for (let i = current + 1; i <= Math.min(total, current + delta); i++) {
    pages.push(i);
  }
  
  // 末页始终显示
  if (total > 1) {
    pages.push(total);
  }
  
  // 添加省略号
  const withEllipsis = [];
  let prev = 0;
  
  for (const page of pages) {
    if (prev + 1 !== page) {
      withEllipsis.push('...');
    }
    withEllipsis.push(page);
    prev = page;
  }
  
  return withEllipsis;
}

const paginationItems = generatePagination(currentPage, totalPages);
---

<div class="flex items-center justify-center space-x-1 sm:space-x-2">
  <!-- 上一页 -->
  {currentPage > 1 ? (
    <a 
      href={`/${section}/${currentPage - 1 === 1 ? '' : currentPage - 1}`} 
      class="inline-flex h-9 w-9 items-center justify-center rounded-md border border-gray-300 bg-white text-sm text-gray-600 hover:border-gray-400 hover:bg-gray-100 dark:border-gray-700 dark:bg-darkmode-theme-light dark:text-gray-400 dark:hover:border-gray-600 dark:hover:bg-gray-800"
    >
      &laquo;
    </a>
  ) : (
    <span class="inline-flex h-9 w-9 cursor-not-allowed items-center justify-center rounded-md border border-gray-200 bg-white text-sm text-gray-300 dark:border-gray-700 dark:bg-darkmode-theme-light dark:text-gray-600">
      &laquo;
    </span>
  )}
  
  <!-- 页码 -->
  {paginationItems.map((item) => {
    if (item === '...') {
      return (
        <span class="inline-flex h-9 items-center justify-center px-2 text-sm text-gray-500 dark:text-gray-400">
          {item}
        </span>
      );
    }
    
    const page = Number(item);
    const isActive = page === currentPage;
    
    return (
      <a 
        href={`/${section}${page === 1 ? '' : `/${page}`}`} 
        class={`inline-flex h-9 w-9 items-center justify-center rounded-md border text-sm ${
          isActive 
            ? 'border-gray-500 bg-gray-500 text-white' 
            : 'border-gray-300 bg-white text-gray-600 hover:border-gray-400 hover:bg-gray-100 dark:border-gray-700 dark:bg-darkmode-theme-light dark:text-gray-400 dark:hover:border-gray-600 dark:hover:bg-gray-800'
        }`}
        aria-current={isActive ? 'page' : undefined}
      >
        {page}
      </a>
    );
  })}
  
  <!-- 下一页 -->
  {currentPage < totalPages ? (
    <a 
      href={`/${section}/${currentPage + 1}`} 
      class="inline-flex h-9 w-9 items-center justify-center rounded-md border border-gray-300 bg-white text-sm text-gray-600 hover:border-gray-400 hover:bg-gray-100 dark:border-gray-700 dark:bg-darkmode-theme-light dark:text-gray-400 dark:hover:border-gray-600 dark:hover:bg-gray-800"
    >
      &raquo;
    </a>
  ) : (
    <span class="inline-flex h-9 w-9 cursor-not-allowed items-center justify-center rounded-md border border-gray-200 bg-white text-sm text-gray-300 dark:border-gray-700 dark:bg-darkmode-theme-light dark:text-gray-600">
      &raquo;
    </span>
  )}
</div> 