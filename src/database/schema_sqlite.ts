import { text, sqliteTable, integer } from 'drizzle-orm/sqlite-core';

// https://orm.drizzle.team/docs/sql-schema-declaration
// This file will contain our database schema

/* {
  "id" : 10001,
  "name" : "宋",
  "intro" : "宋朝是中国历史上承五代十国、下启元朝的时代，分为北宋和南宋。960年，后周大将赵匡胤黄袍加身，建立宋朝。真宗、仁宗时期步入了盛世，北宋初期加强了中央集权，解决了藩镇割据问题。1127年靖康之变，北宋灭亡。宋高宗赵构南迁建立了南宋。后期，抗蒙战争连年，到1276年，元朝军队占领临安，1279年，8岁的小皇帝赵昺被大臣陆秀夫背著跳海而死，厓山海战后，宋朝彻底灭亡。宋朝灭亡是"崖山之后无中国"。两宋时期民族融合和商品经济空前发展，对外交流频繁，文化艺术发展迅速，是中国历史上的黄金时期。 ",
  "start_year" : 960,
  "end_year" : 1279,
  "name_tr" : "宋",
  "intro_tr" : "宋朝是中國歷史上承五代十國、下啓元朝的時代，分爲北宋和南宋。960年，後周大將趙匡胤黃袍加身，建立宋朝。真宗、仁宗時期步入了盛世，北宋初期加強了中央集權，解決了藩鎮割據問題。1127年靖康之變，北宋滅亡。宋高宗趙構南遷建立了南宋。後期，抗蒙戰爭連年，到1276年，元朝軍隊佔領臨安，1279年，8歲的小皇帝趙昺被大臣陸秀夫背著跳海而死，厓山海戰後，宋朝徹底滅亡。宋朝滅亡是"崖山之後無中國"。兩宋時期民族融合和商品經濟空前發展，對外交流頻繁，文化藝術發展迅速，是中國歷史上的黃金時期。 "
} */
export const table_dynasties = sqliteTable('dynasties', {
  id: integer('id').primaryKey(),
  slug: text('slug'),
  name: text('name').notNull(),
  intro: text('intro').notNull(),
  start_year: integer('start_year').notNull(),
  end_year: integer('end_year').notNull(),
  name_tr: text('name_tr').notNull(),
  intro_tr: text('intro_tr').notNull(),
});

/* {
  "id" : 1,
  "show_order" : 1,
  "name" : "选集",
  "limit" : 8,
  "name_tr" : "選集"
} */
export const table_collection_kinds = sqliteTable('collection_kinds', {
  id: integer('id').primaryKey(),
  show_order: integer('show_order').notNull(),
  name: text('name').notNull(),
  limit: integer('limit').notNull(),
  name_tr: text('name_tr').notNull(),
});

/* {
  "id" : 1,
  "show_order" : 5,
  "works_count" : 307,
  "name" : "唐诗三百首",
  "abbr" : null,
  "online_data" : 0,
  "short_desc" : "蘅塘退士编",
  "desc" : "蘅塘退士编撰的唐诗选集。",
  "cover" : "http:\/\/cdn.xcz.im\/img\/wenku\/xj-ts.jpg",
  "link" : "",
  "kind_id" : 1,
  "kind" : "选集",
  "quotes_count" : 442,
  "date" : "",
  "start_date" : "",
  "end_date" : "",
  "press" : "None",
  "book_remote_id" : null,
  "name_tr" : "唐詩三百首",
  "abbr_tr" : null,
  "short_desc_tr" : "蘅塘退士編",
  "desc_tr" : "蘅塘退士編撰的唐詩選集。",
  "kind_tr" : "選集"
} */
export const table_collections = sqliteTable('collections', {
  id: integer('id').primaryKey(),
  slug: text('slug'),
  show_order: integer('show_order').notNull(),
  works_count: integer('works_count').notNull(),
  name: text('name').notNull(),
  // abbr: text('abbr').notNull(),
  online_data: integer('online_data').notNull(),
  short_desc: text('short_desc').notNull(),
  desc: text('desc').notNull(),
  cover: text('cover').notNull(),
  // link: text('link').notNull(),
  kind_id: integer('kind_id').notNull(),
  kind: integer('kind').notNull(),
  quotes_count: integer('quotes_count').notNull(),
  // date: text('date').notNull(),
  // start_date: text('start_date').notNull(),
  // end_date: text('end_date').notNull(),
  // press: text('press').notNull(),
  // book_remote_id: text('book_remote_id').notNull(),
  name_tr: text('name_tr').notNull(),
  // abbr_tr: text('abbr_tr').notNull(),
  short_desc_tr: text('short_desc_tr').notNull(),
  desc_tr: text('desc_tr').notNull(),
  kind_tr: text('kind_tr').notNull(),
});

/* {
  "id" : 1,
  "show_order" : 1,
  "quote_id" : 587,
  "quote" : "兰叶春葳蕤，桂华秋皎洁。",
  "quote_author" : "张九龄",
  "quote_work" : "感遇（其一）",
  "quote_work_id" : 10463,
  "collection_id" : 1,
  "collection_kind_id" : 1,
  "quote_tr" : "蘭葉春葳蕤，桂華秋皎潔。",
  "quote_author_tr" : "張九齡",
  "quote_work_tr" : "感遇（其一）"
} */
export const table_collection_quotes = sqliteTable('collection_quotes', {
  id: integer('id').primaryKey(),
  show_order: integer('show_order').notNull(),
  quote_id:  integer('quote_id').notNull(),
  quote: text('quote').notNull(),
  quote_author: text('quote_author').notNull(),
  quote_work: text('quote_work').notNull(),
  quote_work_id: integer('quote_work_id').notNull(),
  collection_id: integer('collection_id').notNull(),
  collection_kind_id: integer('collection_kind_id').notNull(),
  quote_tr: text('quote_tr').notNull(),
  quote_author_tr: text('quote_author_tr').notNull(),
  quote_work_tr: text('quote_work_tr').notNull(),
});

/* {
  "id" : 1,
  "show_order" : 1,
  "work_id" : 10463,
  "work_remote_id" : "57b2c7a3c4c9710054926358",
  "work_title" : "感遇（其一）",
  "work_full_title" : null,
  "work_author" : "张九龄",
  "work_dynasty" : "唐",
  "work_content" : "兰叶春葳蕤，桂华秋皎洁。",
  "work_kind" : "shi",
  "collection_id" : 1,
  "collection" : "唐诗三百首",
  "work_title_tr" : "",
  "work_full_title_tr" : null,
  "work_author_tr" : "",
  "work_dynasty_tr" : "",
  "work_content_tr" : "",
  "collection_tr" : ""
} */
export const table_collection_works = sqliteTable('collection_works', {
  id: integer('id').primaryKey(),
  show_order: integer('show_order').notNull(),
  work_id: integer('work_id').notNull(),
  // work_remote_id: text('work_remote_id').notNull(),
  work_title: text('work_title').notNull(),
  work_full_title: text('work_full_title').notNull(),
  work_author: text('work_author').notNull(),
  work_dynasty: text('work_dynasty').notNull(),
  work_content: text('work_content').notNull(),
  work_kind: text('work_kind').notNull(),
  collection_id: integer('collection_id').notNull(),
  collection: text('collection').notNull(),
  work_title_tr: text('work_title_tr').notNull(),
  work_full_title_tr: text('work_full_title_tr').notNull(),
  work_author_tr: text('work_author_tr').notNull(),
  work_dynasty_tr: text('work_dynasty_tr').notNull(),
  work_content_tr: text('work_content_tr').notNull(),
  collection_tr: text('collection_tr').notNull(),
})

/* {
  "id" : 10001,
  "name" : "苏轼",
  "intro" : "北宋文学家、书画家，唐宋八大家之一，眉州眉山（今属四川）人，字子瞻，一字和仲，号东坡居士。苏老泉长子，苏颍滨兄。与父、弟合称"三苏"，故又称"大苏"。宋仁宗嘉祐二年（1057年）进士。嘉祐六年（1061年），再中制科，授签书凤翔府节度判官厅事。宋英宗治平二年（1065年），召除判登闻鼓院，寻试馆职，除直史馆。治平三年，父卒，护丧归蜀。宋神宗熙宁二年（1069年），服除，除判官告院兼判尚书祠部，权开封府推官。熙宁四年（1070年），上书论王介甫新法之不便，出为杭州通判。徙知密、徐二州。元丰二年（1079年），移知湖州，因诗托讽，逮赴台狱，史称"乌台诗案"。狱罢，贬黄州团练副使，本州安置。元丰四年（1081年），移汝州团练副使。元丰八年（1085年）春，得请常州居住，十月起知登州。寻召除起居舍人。宋哲宗元祐元年（1086年）迁中书舍人，改翰林学士兼侍读。元祐四年（1089年），以龙图阁学士知杭州。会大旱，饥疾并作，东坡请免上供米，又减价粜常平米，存活甚众。杭近海，民患地泉咸苦，东坡倡浚河通漕，又沿西湖东西三十里修长堤，民德之。元祐六年（1091年），除翰林学士承旨，寻因谗出知颍州，徙扬州。后以端明殿学士、翰林侍读学士出知定州。绍圣元年（1094年），贬惠州。绍圣四年（1097年），再贬儋州。累贬琼州别驾，居昌化。宋徽宗即位，元符三年（1100年）赦还，提举玉局观，复朝奉郎。建中靖国元年（1101年），卒于常州，年六十四（按：东坡生于宋仁宗景祐三年十二月十九日，时已入1037年）。宋孝宗时谥文忠。东坡于文学艺术堪称全才。其文汪洋恣肆，清新畅达，与欧阳文忠并称"欧苏"，为唐宋八大家之一；为诗清新豪健，善用夸张比喻，独具风格，与黄山谷并称"苏黄"；作词开豪放一派，变词体绮靡之风，下启南宋，与辛稼轩并称"苏辛"；工书，擅行、楷，能自创新意，用笔丰腴跌宕，有天真烂漫之趣，与黄山谷、米元章、蔡君谟并称宋四家；画学文与可，喜作枯木怪石，论画主张神似。有《东坡集》四十卷、《东坡后集》二十卷、《和陶诗》四卷、《东坡七集》、《东坡志林》、《东坡乐府》、《仇池笔记》《论语说》等。《全宋诗》东坡诗，卷一至卷四六，以清道光刊王文诰《苏文忠公诗编注集成》为底本，卷四七、卷四八，以清干隆刊冯踵息《苏文忠诗合注》为底本。校以宋刊半叶十行本《东坡集》《东坡后集》（残，简称集甲）、宋刊半叶十二行本《东坡集》《东坡后集》（残，简称集乙，集甲、集乙合称集本）、宋眉山刊《苏文忠公文集》（残，简称集丙）、宋黄州刊《东坡先生后集》（残，简称集丁），宋刊《东坡先生和陶渊明诗》（简称集戊）、宋刊《集注东坡先生诗前集》（残，简称集注）、宋嘉泰刊施德初、顾景繁《注东坡先生诗》（残，简称施甲）、宋景定补刊施、顾《注东坡先生诗》（残，简称施乙，施甲、施乙合称施本）、宋黄善夫家塾刊《王状元集百家注分类东坡先生诗》（简称类甲）、宋泉州刊《王状元集百家注分类东坡先生诗》（残，简称类乙）、元务本书堂刊《增刊校正王状元集注分类东坡先生诗》（简称类丙，类甲、类乙、类丙，合称类本）、明成化刊《东坡七集》（简称七集）、明万历刊《重编东坡先生外集》（简称外集）、清查初白《补注东坡编年诗》（简称查注）、清冯踵息《苏文忠诗合注》（简称合注）。参校资料一为金石碑帖和著录金石诗文的专著的有关部分；一为清人、近人的苏诗校勘批语，其中有何义门焯所校清康熙刊《施注苏诗》（简称何校），卢檠斋、纪晓岚所校清干隆刊查注（分别简称卢校、纪校），章茗簃所校缪艺术风覆明成化《东坡七集》（简称章校）。卷四八所收诗篇除《重编东坡先生外集》外，还分别采自《春渚纪闻》、《侯鲭录》等书，亦据所采各书及有关资料进行校勘。新辑集外诗，编为第四九卷。生平见《宋史·卷三百三十八·苏轼传》。",
  "quotes_count" : 291,
  "views_count" : 18141,
  "dynasty" : "宋",
  "birth_year" : "1037年",
  "death_year" : "1101年",
  "updated_at" : "2019-12-27 17:24:24",
  "baidu_wiki" : "http:\/\/baike.baidu.com\/view\/2517.htm",
  "remote_id" : "57ad1badc4c9710054676d9d",
  "works_count" : 3406,
  "works_shi_count" : 2892,
  "works_ci_count" : 380,
  "works_wen_count" : 122,
  "works_qu_count" : 0,
  "works_fu_count" : 12,
  "name_tr" : "",
  "intro_tr" : "",
  "dynasty_tr" : ""
} */
export const table_authors = sqliteTable('authors', {
  id: integer('id').primaryKey(),
  slug: text('slug'),
  name: text('name').notNull(),
  intro: text('intro').notNull(),
  quotes_count: integer('quotes_count').notNull(),
  views_count: integer('views_count').notNull(),
  dynasty: text('dynasty').notNull(),
  birth_year: text('birth_year').notNull(),
  death_year: text('death_year').notNull(),
  updated_at: text('updated_at').notNull(),
  baidu_wiki: text('baidu_wiki').notNull(),
  remote_id: text('remote_id').notNull(),
  works_count: integer('works_count').notNull(),
  works_shi_count: integer('works_shi_count').notNull(),
  works_ci_count: integer('works_ci_count').notNull(),
  works_wen_count: integer('works_wen_count').notNull(),
  works_qu_count: integer('works_qu_count').notNull(),
  works_fu_count: integer('works_fu_count').notNull(),
  name_tr: text('name_tr').notNull(),
  intro_tr: text('intro_tr').notNull(),
});

/* {
  "id" : 1,
  "quote" : "何当共剪西窗烛，却话巴山夜雨时。",
  "dynasty" : "唐",
  "author_id" : 10009,
  "author" : "李商隐",
  "kind" : "shi",
  "work_id" : 10024,
  "work_title" : "夜雨寄北",
  "updated_at" : "2014-07-23 19:53:31",
  "quote_tr" : "何當共剪西窗燭，卻話巴山夜雨時。",
  "dynasty_tr" : "唐",
  "author_tr" : "李商隱",
  "work_title_tr" : "夜雨寄北"
} */
export const table_quotes = sqliteTable('quotes', {
  id: integer('id').primaryKey(),
  quote: text('quote').notNull(),
  dynasty: text('dynasty').notNull(),
  author_id: integer('author_id').notNull(),
  author: text('author').notNull(),
  kind: text('kind').notNull(),
  work_id: integer('work_id').notNull(),
  work_title: text('work_title').notNull(),
  updated_at: text('updated_at').notNull(),
  quote_tr: text('quote_tr').notNull(),
  dynasty_tr: text('dynasty_tr').notNull(),
  author_tr: text('author_tr').notNull(),
  work_title_tr: text('work_title_tr').notNull(),
})

/*{ 
  "id" : 10001,
  "title" : "临江仙 · 夜归临皋",
  "show_order" : null,
  "posts_count" : 1060,
  "author_works_count" : 3406,
  "quotes_count" : 2,
  "collections_count" : 2,
  "author" : "苏轼",
  "author_desc" : null,
  "author_id" : 10001,
  "author_remote_id" : "57ad1badc4c9710054676d9d",
  "dynasty" : "宋",
  "kind" : "ci",
  "kind_cn" : "词",
  "baidu_wiki" : "http:\/\/baike.baidu.com\/view\/866713.htm",
  "foreword" : "",
  "content" : "夜饮东坡醒复醉，归来仿佛三更。家童鼻息已雷鸣，敲门都不应，倚杖听江声。\r\n长恨此身非我有，何时忘却营营？夜阑风静縠纹平，小舟从此逝，江海寄余生。",
  "intro" : "这首词作于神宗元豐五年，即东坡黄州之贬的第三年，写作者深秋之夜在东坡雪堂开怀畅饮，醉後返归临皋住所的情景，表达了词人退避社会的生活态度和希望彻底解脱的出世意念。词人静夜沉思，豁然有悟，既然自己无法掌握命运，不如全身免祸。是夜风静纹平，心与景会，神与物游。他要趁此良辰美景，驾一叶扁舟，随波流逝，任意东西，将自己的有限生命融化在无限的大自然之中。",
  "annotation" : "临江仙：词牌名，原是唐教坊曲，後用作词牌。《乐章集》入「仙吕调」，《张子野词》入「高平调」，元高拭词注「南吕调」。李後主词名《谢新恩》。贺方回词有「人归落雁後」句，名《雁後归》。韩涧泉词有「罗帐画屏新梦悄」句，名《画屏春》。李易安词有「庭院深深深几许」句，名《庭院深深》。《临江仙》源起颇多歧说。南宋黄叔旸《花庵词选》注，「唐词多缘题所赋，《临江仙》之言水仙，亦其一也。」明董逢元《唐词纪》认为，此调「多赋水媛江妃」，即多为咏水中的女神。调名本意即咏临江凭吊水仙女神。近代学者任半塘先生据敦煌词有句云「岸阔临江底见沙」谓词意涉及临江。「临」本意是俯身看物；临江而看的自是水仙。但中国历代所祭的水仙并不确定。像东汉袁康、吴平《越绝书》所记的春秋吴国功勋伍子胥受谗而死成为长江水仙，东晋王子年《拾遗记》称战国楚大夫屈原为湘江水仙。此外，还有琴高、郭景纯、陶岘（Xiàn）各为不同水仙的记载。另外，投湘江而死的舜之二妃、三国魏曹子建笔下的洛河女神，都是人们祭祀的对象。按《临江仙》调起于唐时，惟以前後阕起句、结句辨体，其前後两起句七字、两结句七字者，以和成绩词为主，无别家可校。其前後两起句七字、两结句四字、五字者，以张子澄词为主，而以牛希济词之起句用韵、李後主词之前後换韵、顾夐（Xiòng）词之结句添字类列。其前後两起句俱六字、两结俱五字两句者，以徐昌图词为主，而以向芗林词之第四句减字类列。其前後两起句俱七字、两结俱五字两句者，以贺方回词为主，而以晏小山词之第二句添字、冯正中词之前後换韵、後阕第四句减字、王逐客词之後阕第四句减字类列。盖词谱专主辨体，原以创始之词、正体者列前，减字、添字者列後，兹从体制编次，稍诠世代，故不能仍按字数多寡也。他调准此。双调小令，五十八字，上下阕各三平韵。约有三格，第三格增二字。此调唱时音节需流丽谐婉，声情掩抑。柳耆卿演为慢曲，《乐章集》又有七十四字一体，九十三字一体，汲古阁本俱刻《临江仙》，今照《花草粹编》校定，一作《临江仙引》，一作《临江仙慢》。\r\n「夜归临皋」：诸本皆无题，唯《东坡外集》注云：「在黄州作。」\r\n鼻息已雷鸣：傅子立注：「韩退之《石鼎联句诗序》：『衡山道士轩辕弥明，与进士刘师服、校书郎侯喜，联《石鼎诗》已毕。道士曰：「此皆不足与语，吾闭口矣。」即倚墙睡，鼻息如雷鸣。』」刘尚荣按：「详见《五百家注〈昌黎文集〉·卷二十一》。按傅注乃引述其大意，未遵原句。」已，《苏长公二妙集》本作「如」。\r\n倚杖：元延祐本原校：「一作『久立』」。\r\n此身非我有：傅子立注引《庄子·卷二十二·〈外篇·知北游〉》：「舜问乎丞曰：『道可得而有乎？』曰：『汝身非汝有也，汝何得有夫道？』舜曰：『吾身非吾有也，孰有之哉？』曰：『是天地之委形也；』」\r\n营营：傅子立注引《庄子·卷二十三·〈杂篇·庚桑楚〉》：「无使汝思虑营营。」\r\n縠（hú）纹：傅子立注：「风息浪平，水纹如縠。《选》诗：『风浪吹纹縠。』」刘尚荣按：「《文选》无此句。疑是王逢《进酒歌》『风过细浪生纹縠』之误引。」\r\n「小舟从此逝，江海寄余生」句：\"余\"一作：\"馀\"。傅子立注：「魏草堂：『何日扁舟去，江上负烟蓑。』杜子美：『张公一生江海客。』」刘尚荣按：「魏句见《宋百家诗》存本《东观集》，题作《暮春闲望》，前句原作『扁舟何日去』，疑傅注误倒。杜句出《洗兵马》，见《九家集注杜诗·卷四》。」",
  "translation" : "夜里在东坡饮酒，醉而复醒，醒了又饮。归来时好像已经是夜半三更了。家童鼾声如雷，反复叫门也不应。衹好拄杖伫立江边聆听江水奔流的声音。\r\n深遗憾身在宦途，我已身不由己。什么时候才能够忘却追逐功名？夜深风静，水波不兴。真想乘上小船从此消逝，在烟波江湖中了却馀生。",
  "master_comment" : "宋·葉石林《避暑录话·巻上》：子瞻在黄州，病赤眼，逾月不出。或疑有他疾，过客遂传以为死矣。有语范景仁于许昌者，景仁绝不置疑，即举袂大恸，召子弟具金帛，遣人赒其家。子弟徐言此传闻未审，当先书以问其安否，得实吊恤之未晚。乃走仆以往，子瞻发书大笑，故後量移汝州，谢表有云：「疾病连年，人皆相传为已死。」未几，复与数客饮江上，夜归，江面际天，风露浩然，有当其意，乃作歌辞，所谓「夜阑风静縠纹平，小舟从此逝，江海寄余生」者，与客大歌数过而散。翌日，喧传子瞻夜作此辞，挂冠服江边，拿舟长啸去矣。郡守徐君猷闻之，惊且惧，以为州失罪人，急命驾往谒，则子瞻鼻鼾如雷，犹未兴也。然此语卒传至京师，虽裕陵亦闻而疑之。",
  "layout" : "indent",
  "highlighted_at" : 1357749647,
  "remote_id" : "57ae79400a2b580063150e39",
  "title_tr" : "",
  "author_tr" : "",
  "author_desc_tr" : null,
  "dynasty_tr" : "",
  "kind_cn_tr" : "",
  "foreword_tr" : "",
  "content_tr" : "",
  "intro_tr" : "",
  "annotation_tr" : null,
  "translation_tr" : null,
  "master_comment_tr" : null,
  "appreciations_tr" : null
} */
export const table_works = sqliteTable('works', {
  id: integer('id').primaryKey(),
  slug: text('slug'),
  title: text('title').notNull(),
  show_order: integer('show_order'),
  posts_count: integer('posts_count').notNull(),
  author_works_count: integer('author_works_count').notNull(),
  quotes_count: integer('quotes_count').notNull(),
  collections_count: integer('collections_count').notNull(),
  author: text('author').notNull(),
  author_desc: text('author_desc').notNull(),
  author_id: integer('author_id').notNull(),
  author_remote_id: text('author_remote_id').notNull(),
  dynasty: text('dynasty').notNull(),
  kind: text('kind').notNull(),
  kind_cn: text('kind_cn').notNull(),
  baidu_wiki: text('baidu_wiki').notNull(),
  foreword: text('foreword').notNull(),
  content: text('content').notNull(),
  intro: text('intro').notNull(),
  annotation: text('annotation').notNull(),
  translation: text('translation').notNull(),
  master_comment: text('master_comment').notNull(),
  layout: text('layout').notNull(),
  highlighted_at: integer('highlighted_at').notNull(),
  // remote_id: text('remote_id').notNull(),
  title_tr: text('title_tr'),
  author_tr: text('author_tr'),
  author_desc_tr: text('author_desc_tr'),
  dynasty_tr: text('dynasty_tr'),
  kind_cn_tr: text('kind_cn_tr'),
  foreword_tr: text('foreword_tr'),
  content_tr: text('content_tr'),
  intro_tr: text('intro_tr'),
  annotation_tr: text('annotation_tr'),
  translation_tr: text('translation_tr'),
  master_comment_tr: text('master_comment_tr'),
  // appreciations_tr: text('appreciations_tr'),
})

// 页面访问统计表
export const table_page_views = sqliteTable('page_views', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  path: text('path').notNull(),  // 页面路径
  page_type: text('page_type').notNull(), // 页面类型：work, author, dynasty, collection, other
  entity_id: integer('entity_id'), // 关联的实体ID（如作品ID、作者ID等）
  views_count: integer('views_count').notNull().default(0), // 总访问次数
  updated_at: text('updated_at').notNull(), // 最后更新时间
});