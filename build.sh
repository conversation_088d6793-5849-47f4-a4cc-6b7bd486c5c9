#!/bin/bash

# 美诗词网站优化构建脚本

echo "=== 开始优化构建流程 ==="

# 创建日志目录
mkdir -p logs

# 清理旧依赖和构建产物
echo "=== 清理缓存和旧构建 ==="
rm -rf node_modules package-lock.json yarn.lock dist/ .astro/

# 安装系统依赖（如果需要）
if command -v yum &> /dev/null; then
  echo "=== 检查系统依赖 ==="
  sudo yum install -y python3 make gcc-c++ sqlite-devel
fi

# 使用yarn安装依赖(更稳定)
echo "=== 安装依赖 ==="
yarn install --frozen-lockfile

# 修复SQLite依赖
echo "=== 修复SQLite依赖 ==="
yarn rebuild better-sqlite3 --update-binary

# 构建前清理缓存
echo "=== 清理缓存 ==="
yarn cache clean

# 修复过长的slug
echo "=== 修复过长的slug ==="
node scripts/fix_long_slugs.js

# 设置更多内存给Node (8GB)
echo "=== 配置构建环境 ==="
export NODE_OPTIONS="--max-old-space-size=8192"

# 开始构建
echo "=== 开始构建项目 ==="
yarn build 2>&1 | tee logs/build.log

# 检查构建结果
if [ -f "dist/server/entry.mjs" ]; then
  echo "=== 构建成功！==="
  echo "现在可以使用PM2启动应用: pm2 start ecosystem.config.js"
else
  echo "=== 构建失败 ==="
  echo "请查看logs/build.log了解详细错误信息"
  exit 1
fi 