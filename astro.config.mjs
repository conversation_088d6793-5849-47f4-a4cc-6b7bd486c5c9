import mdx from "@astrojs/mdx";
import react from "@astrojs/react";
import sitemap from "@astrojs/sitemap";
import tailwind from "@astrojs/tailwind";
import AutoImport from "astro-auto-import";
import { defineConfig, squooshImageService } from "astro/config";
import remarkCollapse from "remark-collapse";
import remarkToc from "remark-toc";
import config from "./src/config/config.json";
import icon from "astro-icon";
import partytown from "@astrojs/partytown";
import node from "@astrojs/node";

// 判断当前环境
const isDev = process.env.NODE_ENV === 'development';

// https://astro.build/config
export default defineConfig({
 vite: {
   server: {
    allowedHosts: ["mokk.cn","localhost",'www.mokk.cn']
   } 
 },
  output: 'server',
  adapter: isDev 
    ? undefined  // 开发环境下不使用适配器
    : node({     // 生产环境使用node适配器
        mode: 'standalone'
      }),
  site: config.site.base_url,
  base: config.site.base_path ? config.site.base_path : "/",
  trailingSlash: config.site.trailing_slash ? "always" : "never",
  image: {
    service: squooshImageService()
  },
  integrations: [
    react(), 
    sitemap({
      changefreq: 'weekly',
      priority: 0.7,
      lastmod: new Date(),
      entryLimit: 10000, // 限制站点地图中的条目数量
      // 自定义需要排除的URL
      filter: (page) => 
        !page.includes('/404') && 
        !page.includes('/admin') && 
        !page.includes('/api/'),
    }), 
    tailwind({
    config: {
      applyBaseStyles: false
    }
    }),
    AutoImport({
    imports: []
    }),
    mdx(),
    icon({
    include: {
      tabler: ['*']
    }
    }),
    partytown({
    config: {
      debug: true,
      forward: ['dataLayer.push']
    }
  })
  ],
  markdown: {
    remarkPlugins: [remarkToc, [remarkCollapse, {
      test: "Table of contents"
    }]],
    shikiConfig: {
      theme: "one-dark-pro",
      wrap: true
    },
    extendDefaultPlugins: true
  }
});
