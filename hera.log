yarn run v1.22.22
$ astro dev
17:37:27 [types] Generated 5ms

 astro  v4.16.18 ready in 2248 ms

┃ Local    http://localhost:4321/
┃ Network  use --host to expose

17:37:28 watching for file changes...
DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

DEPRECATION WARNING [import]: Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

  ╷
6 │   @import "base";
  │           ^^^^^^
  ╵
    src/styles/main.scss 6:11  root stylesheet

DEPRECATION WARNING [import]: Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

  ╷
7 │   @import "fonts";
  │           ^^^^^^^
  ╵
    src/styles/main.scss 7:11  root stylesheet

DEPRECATION WARNING [import]: Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

   ╷
11 │   @import "header";
   │           ^^^^^^^^
   ╵
    src/styles/main.scss 11:11  root stylesheet

DEPRECATION WARNING [import]: Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

   ╷
12 │   @import "components";
   │           ^^^^^^^^^^^^
   ╵
    src/styles/main.scss 12:11  root stylesheet

DEPRECATION WARNING [import]: Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

   ╷
13 │   @import "navigation";
   │           ^^^^^^^^^^^^
   ╵
    src/styles/main.scss 13:11  root stylesheet

WARNING: 3 repetitive deprecation warnings omitted.

middleware, url: /authors
17:38:00 [200] /authors 3018ms
DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

DEPRECATION WARNING [import]: Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

  ╷
6 │   @import "base";
  │           ^^^^^^
  ╵
    src/styles/main.scss 6:11  root stylesheet

DEPRECATION WARNING [import]: Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

  ╷
7 │   @import "fonts";
  │           ^^^^^^^
  ╵
    src/styles/main.scss 7:11  root stylesheet

DEPRECATION WARNING [import]: Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

   ╷
11 │   @import "header";
   │           ^^^^^^^^
   ╵
    src/styles/main.scss 11:11  root stylesheet

DEPRECATION WARNING [import]: Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

   ╷
12 │   @import "components";
   │           ^^^^^^^^^^^^
   ╵
    src/styles/main.scss 12:11  root stylesheet

DEPRECATION WARNING [import]: Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

   ╷
13 │   @import "navigation";
   │           ^^^^^^^^^^^^
   ╵
    src/styles/main.scss 13:11  root stylesheet

WARNING: 3 repetitive deprecation warnings omitted.

middleware, url: /authors/zuoqiuming
17:38:42 [200] /authors/zuoqiuming 580ms
middleware, url: /dynasties
17:38:46 [200] /dynasties 27ms
middleware, url: /authors
17:38:49 [200] /authors 1781ms
middleware, url: /works
17:38:51 [200] /works 751ms
middleware, url: /works
17:38:54 [200] /works 1083ms
middleware, url: /
17:38:57 [200] / 585ms
middleware, url: /authors/banjieyu
17:39:00 [200] /authors/banjieyu 51ms
middleware, url: /authors/liubang
17:39:00 [200] /authors/liubang 19ms
middleware, url: /
17:39:00 [200] / 40ms
middleware, url: /authors/wangbao
17:39:01 [200] /authors/wangbao 27ms
middleware, url: /dynasties/zhou
17:39:05 [200] /dynasties/zhou 1118ms
middleware, url: /api/pageview
17:39:09 [200] POST /api/pageview 28ms
middleware, url: /authors/zisi
17:39:17 [200] /authors/zisi 47ms
middleware, url: /dynasties/zhou
17:39:22 [200] /dynasties/zhou 890ms
middleware, url: /api/pageview
17:39:33 [200] POST /api/pageview 13ms
middleware, url: /dynasties
17:39:41 [200] /dynasties 19ms
middleware, url: /collections
17:39:42 [200] /collections 30ms
middleware, url: /
17:39:44 [200] / 45ms
middleware, url: /works
17:39:46 [200] /works 864ms
middleware, url: /authors
17:39:51 [200] /authors 2539ms
middleware, url: /works/dengleyouyuan
17:39:53 [200] /works/dengleyouyuan 145ms
middleware, url: /works/dengleyouyuan
17:39:56 [200] /works/dengleyouyuan 786ms
middleware, url: /authors/qifuren
17:39:57 [200] /authors/qifuren 661ms
middleware, url: /api/pageview
17:39:57 [200] POST /api/pageview 31ms
middleware, url: /works
17:40:06 [200] /works 1180ms
middleware, url: /authors
17:40:10 [200] /authors 1992ms
middleware, url: /authors/laozi
17:42:12 [200] /authors/laozi 62ms
middleware, url: /dynasties
17:42:15 [200] /dynasties 20ms
middleware, url: /authors
17:42:18 [200] /authors 2041ms
