// 添加slug字段到数据库表的脚本
const Database = require('better-sqlite3');
const pinyin = require('pinyin');
const path = require('path');
const fs = require('fs');

// 转换为拼音并生成slug
function slugify(text) {
  if (!text) return '';
  
  try {
    // 使用pinyin库将中文转换为拼音
    const pinyinArray = pinyin(text, {
      style: pinyin.STYLE_NORMAL, // 不带声调
      heteronym: false // 禁用多音字
    });
    
    // 将拼音数组连接成字符串
    const pinyinText = pinyinArray.map(item => item[0]).join('');
    
    // 处理字符串，确保URL友好
    let slug = pinyinText
      .toLowerCase()
      .replace(/[^\w\s-]/g, '') // 去除特殊字符
      .replace(/[\s_-]+/g, '-') // 用短横线替换空格和下划线
      .replace(/^-+|-+$/g, ''); // 去除开头和结尾的短横线
      
    // 限制slug长度为64个字符，避免文件系统路径限制问题
    if (slug.length > 64) {
      slug = slug.substring(0, 64);
    }
    
    return slug;
  } catch (error) {
    console.error('拼音转换出错:', error);
    // 发生错误时回退到简单模式
    return `item-${text.substring(0, 10)}`; 
  }
}

// 确保slug唯一性
function ensureUniqueSlug(db, tableName, slug, id, attempts = 0) {
  const uniqueSlug = attempts === 0 ? slug : `${slug}-${attempts}`;
  const existingEntry = db.prepare(`SELECT id FROM ${tableName} WHERE slug = ? AND id != ?`).get(uniqueSlug, id);
  
  if (existingEntry) {
    return ensureUniqueSlug(db, tableName, slug, id, attempts + 1);
  }
  return uniqueSlug;
}

// 主函数
function main() {
  try {
    // 连接数据库
    const projectDir = process.cwd();
    console.log('项目目录:', projectDir);
    
    // 根据localdb.ts文件中的配置，数据库位置应该在/src/database/poetry.db
    const dbPath = path.join(projectDir, 'src', 'database', 'poetry.db');
    console.log('数据库路径:', dbPath);
    
    if (!fs.existsSync(dbPath)) {
      console.error('找不到数据库文件:', dbPath);
      console.error('请确保项目已初始化并且数据库已创建。');
      process.exit(1);
    }
    
    const db = new Database(dbPath);
    
    // 仅更新works表的slug，其他表已完成
    console.log('上次操作被中断，现在继续更新作品slug...');
    
    // 使用分批处理避免内存问题
    const batchSize = 100;
    let processed = 0;
    let total = 0;
    
    // 获取总数
    const countResult = db.prepare('SELECT COUNT(*) as count FROM works').get();
    total = countResult.count;
    
    const updateWorkSlug = db.prepare('UPDATE works SET slug = ? WHERE id = ?');
    
    // 批处理更新
    function processBatch(offset) {
      const works = db.prepare('SELECT id, title FROM works LIMIT ? OFFSET ?').all(batchSize, offset);
      
      if (works.length === 0) {
        return false; // 处理完成
      }
      
      for (const work of works) {
        const slug = slugify(work.title);
        const uniqueSlug = ensureUniqueSlug(db, 'works', slug, work.id);
        updateWorkSlug.run(uniqueSlug, work.id);
        processed++;
      }
      
      // 显示进度
      console.log(`更新进度: ${processed}/${total} (${Math.round((processed/total)*100)}%)`);
      
      return true; // 还有更多数据要处理
    }
    
    // 开始批处理
    let offset = 0;
    while(processBatch(offset)) {
      offset += batchSize;
    }
    
    console.log(`成功更新了${processed}个作品slug`);
    console.log('所有数据库迁移任务已完成!');
  } catch (error) {
    console.error('迁移过程中出错:', error);
    process.exit(1);
  }
}

main();
