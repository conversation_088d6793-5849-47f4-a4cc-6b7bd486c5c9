// 修复数据库中过长的slug字段
const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');
const crypto = require('crypto');

// 最大slug长度
const MAX_SLUG_LENGTH = 64;

// 为过长的slug生成短版本
function shortenSlug(slug, id) {
  if (slug.length <= MAX_SLUG_LENGTH) {
    return slug;
  }
  
  // 生成一个基于原始slug和ID的哈希，确保唯一性
  const hash = crypto.createHash('md5').update(`${slug}-${id}`).digest('hex').substring(0, 8);
  
  // 保留原始slug的开头部分，并添加哈希值确保唯一
  return `${slug.substring(0, MAX_SLUG_LENGTH - 9)}-${hash}`;
}

// 确保slug唯一性
function ensureUniqueSlug(db, tableName, slug, id, attempts = 0) {
  const uniqueSlug = attempts === 0 ? slug : `${slug}-${attempts}`;
  const existingEntry = db.prepare(`SELECT id FROM ${tableName} WHERE slug = ? AND id != ?`).get(uniqueSlug, id);
  
  if (existingEntry) {
    return ensureUniqueSlug(db, tableName, slug, id, attempts + 1);
  }
  return uniqueSlug;
}

// 主函数
function main() {
  try {
    console.log('开始修复过长的slug字段...');
    
    // 连接数据库
    const projectDir = process.cwd();
    console.log('项目目录:', projectDir);
    
    // 数据库路径
    const dbPath = path.join(projectDir, 'src', 'database', 'poetry.db');
    console.log('数据库路径:', dbPath);
    
    if (!fs.existsSync(dbPath)) {
      console.error('找不到数据库文件:', dbPath);
      process.exit(1);
    }
    
    const db = new Database(dbPath);
    
    // 修复works表中过长的slug
    const longSlugs = db.prepare(`SELECT id, title, slug FROM works WHERE length(slug) > ${MAX_SLUG_LENGTH}`).all();
    console.log(`发现${longSlugs.length}个过长的slug需要修复`);
    
    if (longSlugs.length === 0) {
      console.log('没有发现过长的slug，无需修复');
      return;
    }
    
    const updateSlug = db.prepare('UPDATE works SET slug = ? WHERE id = ?');
    
    // 开始修复过长的slug
    db.transaction(() => {
      for (const item of longSlugs) {
        const shortenedSlug = shortenSlug(item.slug, item.id);
        const uniqueSlug = ensureUniqueSlug(db, 'works', shortenedSlug, item.id);
        
        updateSlug.run(uniqueSlug, item.id);
        console.log(`修复slug: ${item.id} | ${item.title} | 原长度:${item.slug.length} | 新slug:${uniqueSlug} (${uniqueSlug.length})`);
      }
    })();
    
    console.log(`成功修复了${longSlugs.length}个过长的slug`);
    
    // 检查是否还有过长的slug
    const remainingLongSlugs = db.prepare(`SELECT COUNT(*) as count FROM works WHERE length(slug) > ${MAX_SLUG_LENGTH}`).get();
    if (remainingLongSlugs.count > 0) {
      console.warn(`警告：仍有${remainingLongSlugs.count}个过长的slug未修复`);
    } else {
      console.log('所有过长的slug已修复');
    }
    
  } catch (error) {
    console.error('修复过程中出错:', error);
    process.exit(1);
  }
}

main(); 