/**
 * 页面访问统计数据库迁移脚本
 * 创建表并初始化数据
 */

const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// 数据库路径
const DB_PATH = path.join(__dirname, '..', 'src', 'database', 'poetry.db');

// 确保数据库文件存在
if (!fs.existsSync(DB_PATH)) {
  console.error('数据库文件不存在:', DB_PATH);
  process.exit(1);
}

// 连接数据库
const db = new Database(DB_PATH);

// 开始事务
db.prepare('BEGIN TRANSACTION').run();

try {
  // 检查表是否存在
  const tableExists = db.prepare(`
    SELECT name FROM sqlite_master 
    WHERE type='table' AND name='page_views'
  `).get();

  if (!tableExists) {
    console.log('创建页面访问统计表...');
    
    // 创建页面访问统计表
    db.prepare(`
      CREATE TABLE page_views (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        path TEXT NOT NULL,
        page_type TEXT NOT NULL,
        entity_id INTEGER,
        views_count INTEGER NOT NULL DEFAULT 0,
        updated_at TEXT NOT NULL
      )
    `).run();
    
    // 创建索引
    db.prepare(`
      CREATE INDEX idx_page_views_path ON page_views(path)
    `).run();
    
    db.prepare(`
      CREATE INDEX idx_page_views_entity ON page_views(entity_id, page_type)
    `).run();
    
    console.log('✓ 页面访问统计表创建成功');
  } else {
    console.log('✓ 页面访问统计表已存在');
  }

  // 如果需要，可以在这里添加迁移现有访问数据的代码
  
  // 提交事务
  db.prepare('COMMIT').run();
  console.log('✓ 数据库迁移完成');
} catch (error) {
  // 发生错误，回滚事务
  db.prepare('ROLLBACK').run();
  console.error('数据库迁移失败:', error);
  process.exit(1);
} finally {
  // 关闭数据库连接
  db.close();
} 