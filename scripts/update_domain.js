/**
 * 域名切换脚本 - 用于将网站从临时域名迁移到正式域名
 * 使用方法: node scripts/update_domain.js 新域名
 * 示例: node scripts/update_domain.js meishici.com
 */

const fs = require('fs');
const path = require('path');
const childProcess = require('child_process');

// 获取命令行参数
const newDomain = process.argv[2];

if (!newDomain) {
  console.error('错误: 请提供新域名作为参数');
  console.error('使用方法: node scripts/update_domain.js 新域名');
  console.error('示例: node scripts/update_domain.js meishici.com');
  process.exit(1);
}

// 验证域名格式
if (!/^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z]{2,})+$/.test(newDomain)) {
  console.error('错误: 请提供有效的域名格式');
  process.exit(1);
}

// 配置文件路径
const configPath = path.join(__dirname, '../src/config/config.json');
const nginxPath = path.join(__dirname, '../nginx.conf');

// 1. 更新配置文件中的域名
try {
  console.log(`正在更新网站配置...`);
  
  const config = JSON.parse(fs.readFileSync(configPath, 'utf-8'));
  const oldDomain = config.site.base_url.replace(/^https?:\/\//, '');
  
  config.site.base_url = `https://${newDomain}`;
  
  fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
  
  console.log(`配置文件更新成功: ${oldDomain} → ${newDomain}`);
} catch (error) {
  console.error('更新配置文件失败:', error.message);
  process.exit(1);
}

// 2. 更新Nginx配置
try {
  console.log(`正在更新Nginx配置...`);
  
  let nginxConfig = fs.readFileSync(nginxPath, 'utf-8');
  
  // 获取当前域名
  const serverNameMatch = nginxConfig.match(/server_name\s+([^;]+);/);
  if (!serverNameMatch) {
    throw new Error('无法在Nginx配置中找到server_name指令');
  }
  
  const oldServerNames = serverNameMatch[1].trim().split(/\s+/);
  const oldMainDomain = oldServerNames[0];
  
  // 替换所有server_name指令中的域名
  nginxConfig = nginxConfig.replace(
    /server_name\s+[^;]+;/g, 
    `server_name ${newDomain} www.${newDomain};`
  );
  
  // 替换SSL证书路径
  nginxConfig = nginxConfig.replace(
    new RegExp(`ssl_certificate\\s+/etc/nginx/ssl/${oldMainDomain}.pem;`, 'g'),
    `ssl_certificate /etc/nginx/ssl/${newDomain}.pem;`
  );
  
  nginxConfig = nginxConfig.replace(
    new RegExp(`ssl_certificate_key\\s+/etc/nginx/ssl/${oldMainDomain}.key;`, 'g'),
    `ssl_certificate_key /etc/nginx/ssl/${newDomain}.key;`
  );
  
  fs.writeFileSync(nginxPath, nginxConfig);
  
  console.log(`Nginx配置更新成功: ${oldMainDomain} → ${newDomain}`);
  console.log(`注意: 请确保将SSL证书文件放置在 /etc/nginx/ssl/${newDomain}.pem 和 /etc/nginx/ssl/${newDomain}.key`);
} catch (error) {
  console.error('更新Nginx配置失败:', error.message);
}

// 3. 提醒用户重新构建项目
console.log('\n域名更新完成，请执行以下操作:');
console.log('1. 确保已获取新域名的SSL证书并放置于正确位置');
console.log('2. 重新构建项目: yarn build');
console.log('3. 更新Nginx配置: cp nginx.conf /etc/nginx/nginx.conf');
console.log('4. 重启Nginx: nginx -t && systemctl restart nginx');
console.log('5. 重启应用: pm2 restart meishici');
console.log('\n如需验证更新，请访问: https://' + newDomain); 