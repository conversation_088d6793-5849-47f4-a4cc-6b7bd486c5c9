// 每日更新脚本：更新"每日一诗"和"热门诗词"
const fs = require('fs');
const path = require('path');
const Database = require('better-sqlite3');

// 配置
const JSON_FOLDER = "./.json";
const DB_PATH = path.join(process.cwd(), 'src', 'database', 'poetry.db');
const DATE_JSON_PATH = path.join(JSON_FOLDER, 'date.json');

// 日志函数
function log(message) {
  const now = new Date().toISOString();
  console.log(`[${now}] ${message}`);
}

// 生成一个随机的作品ID，用于"每日一诗"
function getRandomWorkId(db) {
  // 获取作品总数
  const countResult = db.prepare('SELECT COUNT(*) as count FROM works').get();
  const totalWorks = countResult.count;
  
  // 随机选择一个高质量的作品
  // 添加一些条件确保选择的是高质量的诗词，例如内容不为空，字数适中等
  const works = db.prepare(`
    SELECT id FROM works 
    WHERE content IS NOT NULL 
    AND LENGTH(content) BETWEEN 20 AND 500
    ORDER BY RANDOM() 
    LIMIT 1
  `).get();
  
  return works ? works.id : null;
}

// 更新每日一诗
function updateDailyPoem(db) {
  log("开始更新每日一诗...");
  
  try {
    // 确保文件夹存在
    if (!fs.existsSync(JSON_FOLDER)) {
      fs.mkdirSync(JSON_FOLDER, { recursive: true });
    }
    
    // 读取现有date.json文件
    let dateMapping = {};
    if (fs.existsSync(DATE_JSON_PATH)) {
      try {
        dateMapping = JSON.parse(fs.readFileSync(DATE_JSON_PATH, 'utf8'));
      } catch (err) {
        log(`无法解析date.json文件: ${err.message}`);
      }
    }
    
    // 获取今天的日期
    const today = new Date();
    const dateString = today.toISOString().split('T')[0];
    
    // 检查今天的日期是否已存在
    if (dateMapping[dateString]) {
      log(`今天(${dateString})的诗词已存在，ID为: ${dateMapping[dateString]}`);
      return;
    }
    
    // 生成新的每日诗词
    const workId = getRandomWorkId(db);
    if (!workId) {
      log("无法获取随机作品ID");
      return;
    }
    
    // 更新date.json
    dateMapping[dateString] = workId;
    fs.writeFileSync(DATE_JSON_PATH, JSON.stringify(dateMapping, null, 2), 'utf8');
    
    log(`更新每日一诗成功: ${dateString} -> ${workId}`);
  } catch (err) {
    log(`更新每日一诗失败: ${err.message}`);
  }
}

// 更新热门诗词
function updateHotWorks(db) {
  log("开始更新热门诗词...");
  
  try {
    // 获取过去7天内最热门的诗词（基于页面访问次数）
    // 查询page_views表获取最热门的作品ID
    const hotWorkIds = db.prepare(`
      SELECT entity_id, COUNT(*) as view_count
      FROM page_views 
      WHERE page_type = 'work' 
      AND visited_at > datetime('now', '-7 day')
      GROUP BY entity_id
      ORDER BY view_count DESC
      LIMIT 100
    `).all();
    
    if (!hotWorkIds || hotWorkIds.length === 0) {
      log("没有找到热门作品，将使用备选方案");
      
      // 备选方案: 使用quotes_count排序
      const result = db.prepare(`
        UPDATE works
        SET show_order = quotes_count * 10 + random() % 10  -- 添加随机性
        WHERE 1=1
      `).run();
      
      log(`已更新${result.changes}条记录的show_order（基于quotes_count）`);
      return;
    }
    
    // 更新works表的show_order字段，用于排序
    const updateStatement = db.prepare(`
      UPDATE works 
      SET show_order = ?
      WHERE id = ?
    `);
    
    // 开始一个事务进行批量更新
    const updateTransaction = db.transaction((items) => {
      // 先重置所有show_order为0
      db.prepare('UPDATE works SET show_order = 0 WHERE 1=1').run();
      
      // 然后更新热门作品的show_order
      items.forEach((item, index) => {
        const score = 10000 - index * 100 + Math.floor(Math.random() * 10); // 确保排序并添加一点随机性
        updateStatement.run(score, item.entity_id);
      });
    });
    
    // 执行更新事务
    updateTransaction(hotWorkIds);
    
    log(`热门诗词更新成功，已更新${hotWorkIds.length}首诗词的排序`);
  } catch (err) {
    log(`更新热门诗词失败: ${err.message}`);
  }
}

// 主函数
function main() {
  log("====== 开始每日更新任务 ======");
  
  try {
    // 连接数据库
    const db = new Database(DB_PATH);
    
    // 更新每日一诗
    updateDailyPoem(db);
    
    // 更新热门诗词
    updateHotWorks(db);
    
    // 关闭数据库连接
    db.close();
    
    log("====== 每日更新任务完成 ======");
  } catch (err) {
    log(`每日更新任务失败: ${err.message}`);
  }
}

// 运行主函数
main(); 