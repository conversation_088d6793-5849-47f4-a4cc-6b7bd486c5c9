# 美诗词网站部署指南

## 当前部署状态

根据项目配置分析，美诗词网站目前已配置为使用Vercel进行生产环境部署。以下是支持这一结论的证据：

1. **Vercel适配器配置**：
   - 在`astro.config.mjs`文件中，项目使用了Vercel的serverless适配器：
     ```javascript
     import vercel from "@astrojs/vercel/serverless";
     // ...
     export default defineConfig({
       output: 'server',
       adapter: vercel(),
       // ...
     });
     ```

2. **Vercel依赖**：
   - package.json中包含多个Vercel相关依赖：
     ```json
     "@astrojs/vercel": "^7.7.2",
     "@vercel/analytics": "^1.2.2",
     "@vercel/speed-insights": "^1.0.10",
     ```

3. **Vercel部署文件**：
   - 项目中存在`.vercel`目录，其中包含了Vercel部署生成的文件，如`output/_functions`和`output/static`等。

4. **环境变量使用**：
   - 在`src/pages/info.astro`中，代码引用了多个Vercel特定的环境变量：
     ```javascript
     const vercelEnv = import.meta.env.VERCEL_ENV;
     const vercelUrl = import.meta.env.VERCEL_URL;
     const gitCommitSha = import.meta.env.VERCEL_GIT_COMMIT_SHA;
     ```

## 部署到腾讯云指南

要将美诗词网站从Vercel迁移到腾讯云，您可以考虑以下几种方案：

### 方案一：使用腾讯云Web应用托管服务

腾讯云Web应用托管服务是一个类似于Vercel的平台即服务(PaaS)产品，适合托管Astro等现代Web应用。

#### 步骤：

1. **修改Astro配置**：
   - 在`astro.config.mjs`中，将Vercel适配器替换为Node适配器：
   ```javascript
   // 移除 import vercel from "@astrojs/vercel/serverless";
   import node from "@astrojs/node";
   
   export default defineConfig({
     output: 'server',
     adapter: node({
       mode: 'standalone'
     }),
     // 其他配置保持不变
   });
   ```

2. **安装Node适配器**：
   ```bash
   yarn add @astrojs/node
   ```

3. **构建项目**：
   ```bash
   yarn build
   ```

4. **登录腾讯云控制台**：
   - 访问[腾讯云Web应用托管服务](https://console.cloud.tencent.com/webify)
   - 创建新应用
   - 关联您的代码仓库或上传构建后的文件
   - 配置构建命令为`yarn build`
   - 配置输出目录为`dist`

5. **配置环境变量**：
   - 在腾讯云Web应用托管服务控制台中，添加与Vercel相同的环境变量

### 方案二：使用腾讯云云函数SCF + API网关

对于需要服务器端渲染(SSR)的Astro应用，可以使用腾讯云云函数(SCF)和API网关。

#### 步骤：

1. **修改Astro配置**：
   - 创建一个腾讯云云函数适配器文件`adapter-tencent.js`：
   ```javascript
   // 这是一个简化示例，实际实现可能需要更复杂的逻辑
   export default function createAdapter() {
     return {
       name: 'tencent-scf',
       serverEntrypoint: '@astrojs/node/server.js',
       exports: ['handler'],
     };
   }
   ```

2. **更新Astro配置**：
   ```javascript
   import tencentAdapter from './adapter-tencent.js';
   
   export default defineConfig({
     output: 'server',
     adapter: tencentAdapter(),
     // 其他配置保持不变
   });
   ```

3. **创建云函数处理程序**：
   - 创建`scf-handler.js`文件：
   ```javascript
   exports.handler = async (event, context) => {
     // 导入Astro生成的服务器入口点
     const { handler } = await import('./dist/server/entry.mjs');
     
     // 转换API网关事件为HTTP请求格式
     const request = createRequestFromEvent(event);
     
     // 调用Astro处理程序
     const response = await handler(request);
     
     // 转换响应为API网关格式
     return formatResponseForAPIGateway(response);
   };
   ```

4. **部署到腾讯云**：
   - 使用[Serverless Framework](https://github.com/serverless/serverless)创建部署配置
   - 配置API网关触发器
   - 执行部署命令

### 方案三：使用腾讯云轻量应用服务器

如果您希望更直接地控制服务器环境，可以使用腾讯云轻量应用服务器。

#### 步骤：

1. **购买轻量应用服务器**：
   - 在腾讯云控制台购买轻量应用服务器
   - 选择Node.js应用模板或基础系统镜像

2. **配置服务器环境**：
   - 安装Node.js和Yarn
   - 安装PM2用于进程管理

3. **部署应用**：
   - 使用Git克隆代码仓库到服务器
   - 安装依赖：`yarn install`
   - 构建应用：`yarn build`
   - 使用PM2启动应用：
     ```bash
     pm2 start yarn --name "meishici" -- preview
     ```

4. **配置Nginx**：
   - 安装Nginx作为反向代理
   - 配置域名和SSL证书
   - 配置Nginx将请求转发到Astro应用

5. **设置自动部署**：
   - 配置Git Hooks或使用CI/CD工具实现自动部署

## 注意事项

1. **数据库迁移**：
   - 项目使用SQLite数据库，确保将数据库文件一同迁移
   - 考虑备份策略和数据一致性

2. **环境变量**：
   - 确保在腾讯云环境中设置所有必要的环境变量
   - 特别注意替换Vercel特定的环境变量

3. **静态资源**：
   - 考虑使用腾讯云对象存储(COS)来存储和提供静态资源
   - 配置CDN加速静态资源访问

4. **域名和SSL**：
   - 在腾讯云DNS解析服务中配置域名
   - 申请并配置SSL证书

5. **监控和日志**：
   - 配置腾讯云监控和日志服务
   - 设置告警机制

## 结论

美诗词网站目前已配置为使用Vercel进行生产部署。迁移到腾讯云是可行的，但需要进行一些配置调整。根据您的具体需求和技术熟悉度，可以选择上述三种方案中的一种进行部署。

无论选择哪种方案，都建议先在测试环境中验证部署流程，确保应用在腾讯云环境中正常运行后再切换生产流量。