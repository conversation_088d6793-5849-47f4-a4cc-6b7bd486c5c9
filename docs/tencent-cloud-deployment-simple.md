# 美诗词网站部署到腾讯云轻量服务器简易指南

本指南专为非技术用户设计，帮助您将GitHub上的美诗词网站代码轻松部署到腾讯云轻量应用服务器。

## 准备工作

1. 腾讯云轻量应用服务器（已购买）
2. 服务器登录信息（IP地址、用户名、密码）
3. GitHub上的代码仓库地址

## 部署步骤

### 第一步：连接到服务器

使用SSH工具连接到您的服务器：

- **Windows用户**：使用PuTTY等SSH客户端
- **Mac/Linux用户**：使用终端

```bash
ssh root@您的服务器IP
```

输入密码登录。

### 第二步：安装必要软件

复制以下命令，粘贴到服务器终端中执行：

```bash
# 更新系统并安装基础工具
yum update -y
yum install -y git wget curl vim

# 安装Node.js 18
curl -fsSL https://rpm.nodesource.com/setup_18.x | bash -
yum install -y nodejs

# 检查Node.js安装
node -v
npm -v

# 安装PM2进程管理器
npm install -g pm2

# 安装Nginx
yum install -y nginx
systemctl enable nginx
systemctl start nginx
```

### 第三步：创建网站目录并克隆代码

```bash
# 创建网站目录
mkdir -p /var/www/meishici
cd /var/www/meishici

# 从GitHub克隆代码（替换GitHub仓库地址）
git clone https://github.com/wuduwa/meishici.git 
```

### 第四步：安装依赖并构建项目

```bash
# 安装项目依赖
npm install

# 构建项目
npm run build
```

### 第五步：启动网站服务

```bash
# 使用PM2启动服务
pm2 start ecosystem.config.js

# 查看服务状态
pm2 list
```

### 第六步：配置Nginx反向代理

```bash
# 创建Nginx配置文件
cat > /etc/nginx/conf.d/meishici.conf << 'EOF'
server {
    listen 80;
    server_name 您的服务器IP;

    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

# 检查Nginx配置
nginx -t

# 重启Nginx
systemctl restart nginx
```

### 第七步：开放防火墙端口

```bash
# 开放HTTP端口
firewall-cmd --permanent --add-service=http
firewall-cmd --reload
```

## 访问您的网站

现在，您可以通过浏览器访问您的网站了：

```
http://您的服务器IP
```

## 更新网站

当您的GitHub仓库有更新时，可以使用以下命令更新网站：

```bash
# 进入网站目录
cd /var/www/meishici

# 运行部署脚本
./deploy.sh
```

## 常见问题解决

1. **网站无法访问**：
   - 检查PM2服务是否运行：`pm2 list`
   - 检查Nginx是否运行：`systemctl status nginx`
   - 检查防火墙设置：`firewall-cmd --list-all`

2. **更新网站失败**：
   - 检查部署日志：`./deploy.sh`
   - 检查PM2日志：`pm2 logs meishici`

3. **服务器重启后网站不可用**：
   - 设置PM2开机自启：`pm2 startup` 然后执行显示的命令
   - 保存PM2进程列表：`pm2 save`

## 恭喜！

您已成功将美诗词网站部署到腾讯云轻量应用服务器。如有任何问题，请参考上述故障排除步骤或联系技术支持。