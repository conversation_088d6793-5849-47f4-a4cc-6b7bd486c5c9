# 美诗词网站部署到腾讯云轻量服务器超简易指南

> 本指南专为完全不懂技术的用户设计，提供最简单的步骤将GitHub上的美诗词网站代码部署到腾讯云轻量应用服务器。

## 准备工作

在开始之前，请确保您已经：

1. 购买了腾讯云轻量应用服务器
2. 有服务器的登录信息（IP地址、用户名、密码）
3. 已将代码上传到GitHub

## 部署步骤

### 第一步：连接到您的服务器

#### Windows用户

1. 下载并安装[PuTTY](https://www.putty.org/)
2. 打开PuTTY
3. 在「Host Name」框中输入您的服务器IP地址
4. 点击「Open」按钮
5. 在弹出的窗口中输入用户名（通常是`root`）和密码

#### Mac/Linux用户

1. 打开「终端」应用
2. 输入命令：`ssh root@您的服务器IP`（替换为您的实际IP地址）
3. 输入密码

### 第二步：复制粘贴以下命令

连接到服务器后，依次复制以下命令块，粘贴到终端中，然后按回车键执行。

```bash
# 更新系统并安装必要工具
yum update -y
yum install -y git wget curl vim
```

```bash
# 安装Node.js
curl -fsSL https://rpm.nodesource.com/setup_20.x | bash -
yum install -y nodejs
```

```bash
# 安装PM2和Nginx
npm install -g pm2
yum install -y nginx
systemctl enable nginx
systemctl start nginx
```

```bash
# 创建网站目录
mkdir -p /var/www/meishici
cd /var/www/meishici
```

```bash
# 从GitHub克隆代码（替换为您的GitHub仓库地址）
git clone https://github.com/wuduwa/meishici.git
```

```bash
# 安装项目依赖
npm install
```

```bash
# 构建项目
npm run build
```

```bash
# 启动网站服务
pm2 start ecosystem.config.js
```

```bash
# 配置Nginx（一步完成）
cat > /etc/nginx/conf.d/meishici.conf << 'EOF'
server {
    listen 80;
    server_name _;

    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

nginx -t && systemctl restart nginx
```

```bash
# 开放防火墙端口
firewall-cmd --permanent --add-service=http
firewall-cmd --reload
```

### 第三步：访问您的网站

现在，打开浏览器，输入您服务器的IP地址，就可以访问您的网站了！

```
http://您的服务器IP
```

## 更新网站

当您的GitHub仓库有更新时，只需执行以下命令更新网站：

```bash
# 进入网站目录
cd /var/www/meishici

# 运行部署脚本
./deploy.sh
```

## 常见问题

### 网站无法访问？

执行以下命令检查服务状态：

```bash
# 检查网站服务是否运行
pm2 list

# 检查Nginx是否运行
systemctl status nginx
```

### 如何查看错误日志？

```bash
# 查看网站日志
pm2 logs meishici
```

### 服务器重启后网站不可用？

执行以下命令设置自动启动：

```bash
pm2 startup
# 执行上面命令输出的指令
pm2 save
```

## 恭喜！

您已成功部署美诗词网站到腾讯云服务器！如有任何问题，可以参考上述故障排除步骤。