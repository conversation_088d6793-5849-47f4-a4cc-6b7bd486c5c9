# 首页内容每日更新指南

本文档介绍如何实现首页"每日一诗"和"热门诗词"的自动更新功能。

## 功能说明

1. **每日一诗**: 每天自动从数据库中随机选择一首优质诗词作为"每日一诗"展示在首页。
2. **热门诗词**: 根据过去7天内的页面访问统计，每天更新首页展示的热门诗词列表。

## 实现原理

### 每日一诗更新

1. 脚本会读取并更新`.json/date.json`文件，该文件存储了日期与诗词ID的映射关系。
2. 每次执行时，脚本会检查当天日期是否已有对应的诗词ID。如果没有，则随机选择一首优质诗词并建立映射。
3. 首页加载时会读取date.json文件，获取当天对应的诗词并展示。

### 热门诗词更新

1. 脚本会查询`page_views`表，统计过去7天内访问量最高的诗词作品。
2. 根据访问量，更新`works`表中的`show_order`字段值。
3. 首页通过`show_order`字段降序排序获取热门诗词列表。

## 使用方法

### 手动执行更新

```bash
# 进入项目目录
cd /var/www/meishici

# 执行更新脚本
yarn update_daily
```

### 设置自动更新（推荐）

我们提供了一个自动设置定时任务的脚本，会在每天凌晨3点自动执行更新：

```bash
# 进入项目目录
cd /var/www/meishici

# 执行定时任务设置脚本（需要root权限）
sudo ./setup_cron.sh
```

执行后，脚本会：
1. 创建必要的日志目录
2. 配置执行脚本
3. 添加crontab任务
4. 显示当前设置的定时任务列表

### 查看更新日志

更新日志会保存在`/var/www/meishici/logs/daily_update.log`文件中，可以通过以下命令查看：

```bash
# 查看最近的更新日志
tail -f /var/www/meishici/logs/daily_update.log
```

## 定制和调整

### 修改随机诗词的选择条件

如果需要调整"每日一诗"的选择条件，可以编辑`scripts/update_daily.js`文件中的`getRandomWorkId`函数：

```javascript
function getRandomWorkId(db) {
  // 随机选择一个高质量的作品
  const works = db.prepare(`
    SELECT id FROM works 
    WHERE content IS NOT NULL 
    AND LENGTH(content) BETWEEN 20 AND 500  // 可以调整诗词长度范围
    ORDER BY RANDOM() 
    LIMIT 1
  `).get();
  
  return works ? works.id : null;
}
```

### 修改热门诗词的计算方式

如需调整热门诗词的计算方式，可以编辑`scripts/update_daily.js`文件中的`updateHotWorks`函数。

### 修改更新频率

默认设置为每天凌晨3点更新一次。如需修改更新频率，可以编辑crontab设置：

```bash
# 编辑当前用户的crontab
crontab -e
```

然后修改对应的cron表达式。例如，改为每12小时执行一次：

```
0 */12 * * * /var/www/meishici/run_daily_update.sh
```

## 故障排除

### 更新未生效

1. 检查日志文件：`/var/www/meishici/logs/daily_update.log`
2. 确认crontab任务已正确设置：`crontab -l`
3. 检查脚本权限：`ls -la /var/www/meishici/run_daily_update.sh`
4. 手动执行脚本测试：`/var/www/meishici/run_daily_update.sh`

### 其他问题

如遇到其他问题，请查看日志文件或联系网站管理员。 