# 美诗词网站GitHub部署到腾讯云指南

本指南将帮助您将美诗词网站代码上传到GitHub，并通过GitHub部署到腾讯云轻量应用服务器。

## 第一部分：上传代码到GitHub

### 1. 初始化Git仓库（已完成）

```bash
# 在项目根目录下初始化Git仓库
git init
```

### 2. 添加文件到Git仓库

```bash
# 添加所有文件到暂存区
git add .

# 如果不想添加某些文件，可以查看.gitignore文件确保它们被排除
```

### 3. 提交代码到本地仓库

```bash
# 提交代码并添加描述信息
git commit -m "初始化美诗词网站代码"
```

### 4. 在GitHub上创建新仓库

1. 登录您的GitHub账号
2. 点击右上角的"+"按钮，选择"New repository"
3. 填写仓库信息：
   - Repository name: `meishici`（或您喜欢的名称）
   - Description: 美诗词网站 - 中国古诗词展示平台
   - 选择公开(Public)或私有(Private)
   - 不要初始化仓库（不勾选"Initialize this repository with a README"）
4. 点击"Create repository"按钮

### 5. 将本地仓库与GitHub仓库关联

```bash
# 添加远程仓库地址（将URL替换为您的GitHub仓库地址）
git remote add origin https://github.com/您的用户名/meishici.git

# 将本地main分支推送到远程仓库
git branch -M main
git push -u origin main
```

## 第二部分：部署到腾讯云轻量应用服务器

### 1. 购买并设置腾讯云轻量应用服务器

按照`docs/tencent-deployment-guide.md`文件中的"前期准备"和"服务器环境配置"部分进行操作。

### 2. 在服务器上克隆GitHub仓库

```bash
# 连接到您的腾讯云服务器
ssh root@您的服务器IP

# 创建网站目录
mkdir -p /var/www/meishici
cd /var/www/meishici

# 克隆GitHub仓库
git clone https://github.com/您的用户名/meishici.git .
```

### 3. 安装依赖并构建项目

```bash
# 安装依赖
yarn install

# 构建项目
yarn build
```

### 4. 使用PM2启动应用

```bash
# 使用PM2启动应用
pm2 start yarn --name "meishici" -- preview

# 设置PM2开机自启
pm2 save
pm2 startup
```

### 5. 配置Nginx反向代理

按照`docs/tencent-deployment-guide.md`文件中的"配置Nginx"部分进行操作。

## 第三部分：设置自动部署

### 1. 在服务器上创建部署脚本

```bash
# 创建部署脚本
vi /var/www/deploy.sh
```

添加以下内容：

```bash
#!/bin/bash

cd /var/www/meishici
git pull
yarn install
yarn build
pm2 restart meishici
```

```bash
# 添加执行权限
chmod +x /var/www/deploy.sh
```

### 2. 设置GitHub Webhook

1. 在服务器上安装webhook处理工具

```bash
yum install -y nodejs npm
npm install -g github-webhook-handler
```

2. 创建webhook处理脚本

```bash
vi /var/www/webhook.js
```

添加以下内容：

```javascript
const http = require('http');
const createHandler = require('github-webhook-handler');
const { exec } = require('child_process');

// 替换为您在GitHub中设置的Secret
const handler = createHandler({ path: '/webhook', secret: '您的密钥' });

http.createServer((req, res) => {
  handler(req, res, function (err) {
    res.statusCode = 404;
    res.end('no such location');
  });
}).listen(7777);

handler.on('error', function (err) {
  console.error('Error:', err.message);
});

handler.on('push', function (event) {
  console.log('Received a push event for %s to %s',
    event.payload.repository.name,
    event.payload.ref);
  
  // 只处理main分支的推送
  if (event.payload.ref === 'refs/heads/main') {
    exec('/var/www/deploy.sh', (error, stdout, stderr) => {
      if (error) {
        console.error(`执行错误: ${error}`);
        return;
      }
      console.log(`stdout: ${stdout}`);
      console.error(`stderr: ${stderr}`);
    });
  }
});

console.log('Webhook服务器已启动，监听端口7777');
```

3. 使用PM2启动webhook服务

```bash
pm2 start /var/www/webhook.js --name "github-webhook"
pm2 save
```

4. 配置Nginx转发webhook请求

```bash
vi /etc/nginx/conf.d/meishici.conf
```

在server块中添加：

```nginx
location /webhook {
    proxy_pass http://127.0.0.1:7777;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_cache_bypass $http_upgrade;
}
```

重启Nginx：

```bash
nginx -t
systemctl restart nginx
```

5. 在GitHub仓库中设置Webhook

- 进入您的GitHub仓库
- 点击"Settings" > "Webhooks" > "Add webhook"
- 填写以下信息：
  - Payload URL: `http://您的域名/webhook`
  - Content type: `application/json`
  - Secret: 与webhook.js中设置的密钥相同
  - 选择"Just the push event"
  - 勾选"Active"
- 点击"Add webhook"按钮

## 测试自动部署

1. 在本地修改代码
2. 提交并推送到GitHub

```bash
git add .
git commit -m "测试自动部署"
git push
```

3. 观察服务器上的部署日志

```bash
pm2 logs github-webhook
pm2 logs meishici
```

## 故障排除

如果自动部署不工作，请检查：

1. webhook服务是否正常运行：`pm2 status`
2. webhook日志中是否有错误：`pm2 logs github-webhook`
3. GitHub webhook设置中是否显示成功发送请求
4. 服务器防火墙是否允许GitHub的请求

## 安全建议

1. 使用HTTPS而非HTTP接收webhook请求
2. 为GitHub webhook设置一个强密钥
3. 考虑使用SSH密钥而非HTTPS克隆仓库，避免在脚本中存储密码
4. 定期更新服务器系统和软件包