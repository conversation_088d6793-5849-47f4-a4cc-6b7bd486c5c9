{"name": "me<PERSON>ci", "version": "1.0.0", "description": "美诗词，领略中国古代诗词之美", "author": "me<PERSON>ci", "license": "MIT", "packageManager": "yarn@1.22.19", "scripts": {"dev": "astro dev", "dev:full": "yarn build_search && yarn build_date && yarn migrate_pageviews && astro dev", "build": "yarn build_search && yarn build_date && yarn migrate_pageviews && yarn fix_long_slugs && astro build --verbose", "fix_long_slugs": "node scripts/fix_long_slugs.js", "update_daily": "node scripts/update_daily.js", "preview": "astro preview", "format": "prettier -w ./src", "generate": "drizzle-kit generate:sqlite", "studio": "drizzle-kit studio", "hide-toolbar": "astro preferences disable dev<PERSON><PERSON><PERSON>", "show-toolbar": "astro preferences enable devToolbar", "build_search": "node scripts/build_search.js", "build_date": "node scripts/build_date.js", "remove-darkmode": "node scripts/removeDarkmode.js && yarn format", "add_slugs": "node scripts/add_slug_fields.js", "migrate_pageviews": "node scripts/migrate_pageviews.js", "start": "node dist/server/entry.mjs"}, "dependencies": {"@astrojs/mdx": "^2.0.3", "@astrojs/node": "^6.0.4", "@astrojs/partytown": "^2.0.2", "@astrojs/react": "^3.0.7", "@astrojs/rss": "^4.0.1", "@astrojs/sitemap": "^3.0.3", "@astrojs/solid-js": "^3.0.2", "@astrojs/tailwind": "^5.0.3", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tooltip": "^1.0.7", "astro": "^4.0.7", "astro-auto-import": "^0.4.1", "astro-font": "^0.0.77", "astro-icon": "^1.0.1", "astro-loading-indicator": "^0.4.0", "better-sqlite3": "^9.2.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "drizzle-orm": "^0.28.6", "github-slugger": "^2.0.0", "gray-matter": "^4.0.3", "limax": "^4.1.0", "lucide-react": "^0.263.1", "marked": "^9.1.0", "next-themes": "^0.2.1", "pinyin": "2.9.1", "postgres": "^3.3.5", "prettier-plugin-astro": "^0.12.1", "prettier-plugin-tailwindcss": "^0.5.9", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^4.11.0", "react-lite-youtube-embed": "^2.3.52", "remark-collapse": "^0.1.2", "remark-toc": "^9.0.0", "solid-js": "^1.8.5", "sonner": "^1.2.0", "swiper": "^11.0.4", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@iconify-json/tabler": "^1.1.100", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@types/better-sqlite3": "^7.6.4", "@types/marked": "^5.0.1", "@types/node": "^20.8.6", "@types/react": "^18.2.28", "@types/react-dom": "^18.2.13", "autoprefixer": "^10.4.16", "drizzle-kit": "^0.19.13", "eslint": "^8.51.0", "postcss": "^8.4.31", "prettier": "^3.0.3", "prettier-plugin-astro": "^0.12.1", "prettier-plugin-tailwindcss": "^0.5.9", "sass": "^1.86.3", "sharp": "^0.32.6", "tailwind-bootstrap-grid": "^5.0.1", "tailwindcss": "^3.3.3", "typescript": "^5.2.2"}}